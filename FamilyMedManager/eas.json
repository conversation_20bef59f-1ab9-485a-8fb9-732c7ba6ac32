{
  "cli": {
    "version": ">= 16.19.3",
    "appVersionSource": "remote"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "simulator": true
      }
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "simulator": false
      },
      "android": {
        "buildType": "apk"
      }
    },
    "production": {
      "autoIncrement": true,
      "env": {
        "EXPO_PUBLIC_OPENAI_API_KEY": "your_openai_api_key_here" // Replace with your actual OpenAI API key
      },
      "ios": {
        "simulator": false
      },
      "android": {
        "buildType": "apk"
      }
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "**********", // must be digits only (replace with your App Store Connect App ID)
        "appleTeamId": "8SPGS8QFVJ" // must be 10 uppercase letters or digits (replace with your Apple Team ID)
      },
      "android": {
        "serviceAccountKeyPath": "path/to/api-key.json",
        "track": "internal"
      }
    }
  }
}