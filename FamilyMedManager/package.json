{"name": "familymedmanager", "main": "expo-router/entry", "version": "1.0.1", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "expo lint", "build:development": "eas build --profile development", "build:preview": "eas build --profile preview", "build:production": "eas build --profile production", "submit:ios": "eas submit --platform ios", "submit:android": "eas submit --platform android", "test": "jest"}, "dependencies": {"@expo/metro-runtime": "~6.1.2", "@expo/vector-icons": "^15.0.2", "@react-navigation/bottom-tabs": "^7.4.0", "@react-navigation/elements": "^2.6.3", "@react-navigation/native": "^7.1.8", "expo": "~54.0.10", "expo-constants": "~18.0.9", "expo-font": "~14.0.8", "expo-haptics": "~15.0.7", "expo-image": "~3.0.8", "expo-linear-gradient": "~15.0.7", "expo-linking": "~8.0.8", "expo-router": "~6.0.8", "expo-splash-screen": "~31.0.10", "expo-sqlite": "~16.0.8", "expo-status-bar": "~3.0.8", "expo-symbols": "~1.0.7", "expo-system-ui": "~6.0.7", "expo-web-browser": "~15.0.7", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.4", "react-native-gesture-handler": "~2.28.0", "react-native-reanimated": "~4.1.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-web": "~0.21.0", "react-native-worklets": "0.5.1"}, "devDependencies": {"@types/react": "~19.1.0", "@types/jest": "^29.5.3", "typescript": "~5.9.2", "eslint": "^9.25.0", "eslint-config-expo": "~10.0.0", "jest": "^29.7.0", "jest-expo": "~52.0.0"}, "private": true, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)"]}}