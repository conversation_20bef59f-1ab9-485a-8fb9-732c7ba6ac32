# Production Environment Configuration for FamilyMedManager
# Copy this file to .env and fill in your actual values

# OpenAI API Configuration
# Get your API key from: https://platform.openai.com/api-keys
EXPO_PUBLIC_OPENAI_API_KEY=your_production_openai_api_key_here

# App Configuration
EXPO_PUBLIC_APP_ENV=production
EXPO_PUBLIC_API_URL=https://api.familymedmanager.com

# Analytics (if you plan to add analytics)
# EXPO_PUBLIC_ANALYTICS_KEY=your_analytics_key_here

# Sentry (if you plan to add error tracking)
# EXPO_PUBLIC_SENTRY_DSN=your_sentry_dsn_here

# Note: In Expo, environment variables that should be accessible in the client
# must be prefixed with EXPO_PUBLIC_
