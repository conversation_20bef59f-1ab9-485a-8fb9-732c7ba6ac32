import Layout from '../components/Layout'

export default function About() {
    return (
        <Layout>
            <div className="about-page">
                {/* Hero Section */}
                <section className="page-hero">
                    <h1>About FamilyMedManager</h1>
                    <p className="hero-subtitle">
                        Empowering families to take control of their health through intelligent medication management
                    </p>
                </section>

                {/* Mission Section */}
                <section className="content-section">
                    <h2>Our Mission</h2>
                    <p>
                        At FamilyMedManager, we believe that managing your family's health shouldn't be complicated or stressful.
                        Our mission is to provide families with a comprehensive, secure, and intelligent solution for tracking
                        medications, managing dosages, and ensuring that critical health information is always at your fingertips.
                    </p>
                    <p>
                        We understand that every family is unique, with different health needs, schedules, and challenges.
                        That's why we've built a flexible platform that adapts to your family's specific requirements while
                        maintaining the highest standards of privacy and security.
                    </p>
                </section>

                {/* Story Section */}
                <section className="content-section">
                    <h2>Our Story</h2>
                    <p>
                        FamilyMedManager was born from a simple yet critical need: the challenge of keeping track of multiple
                        family members' medications, schedules, and health information. As families grow and health needs become
                        more complex, traditional methods of medication management often fall short.
                    </p>
                    <p>
                        Our team of healthcare professionals, software engineers, and user experience designers came together
                        to create a solution that combines cutting-edge technology with practical, everyday usability. We've
                        integrated AI-powered search capabilities, comprehensive inventory management, and intelligent scheduling
                        to create a platform that truly serves families' needs.
                    </p>
                </section>

                {/* Values Section */}
                <section className="content-section">
                    <h2>Our Values</h2>
                    <div className="values-grid">
                        <div className="value-item">
                            <h3>🔒 Privacy First</h3>
                            <p>
                                Your family's health information is deeply personal. We use local storage and offline-first
                                architecture to ensure your data stays private and secure.
                            </p>
                        </div>
                        <div className="value-item">
                            <h3>🎯 User-Centered Design</h3>
                            <p>
                                Every feature is designed with real families in mind. We prioritize simplicity, accessibility,
                                and intuitive interfaces that work for users of all ages.
                            </p>
                        </div>
                        <div className="value-item">
                            <h3>🚀 Innovation</h3>
                            <p>
                                We leverage the latest technologies, including AI and machine learning, to provide intelligent
                                recommendations and streamline medication management.
                            </p>
                        </div>
                        <div className="value-item">
                            <h3>🤝 Reliability</h3>
                            <p>
                                When it comes to health, reliability is non-negotiable. Our platform is built for consistency,
                                accuracy, and dependable performance when you need it most.
                            </p>
                        </div>
                    </div>
                </section>

                {/* Technology Section */}
                <section className="content-section">
                    <h2>Technology & Innovation</h2>
                    <p>
                        FamilyMedManager is built on a modern, robust technology stack designed for performance, security, and scalability:
                    </p>
                    <ul className="tech-list">
                        <li><strong>Cross-Platform Compatibility:</strong> Built with React Native and Expo for seamless iOS, Android, and Web experiences</li>
                        <li><strong>AI-Powered Intelligence:</strong> Integration with OpenAI's GPT-4 for intelligent medication recommendations and first aid guidance</li>
                        <li><strong>Secure Local Storage:</strong> SQLite database with offline-first architecture for maximum privacy and reliability</li>
                        <li><strong>Modern Architecture:</strong> File-based routing, custom theme system, and responsive design for optimal user experience</li>
                        <li><strong>Healthcare Standards:</strong> Built with healthcare data privacy and security best practices in mind</li>
                    </ul>
                </section>

                {/* Team Section */}
                <section className="content-section">
                    <h2>Our Commitment</h2>
                    <p>
                        We're committed to continuously improving FamilyMedManager based on user feedback and evolving healthcare needs.
                        Our development team regularly updates the platform with new features, security enhancements, and usability improvements.
                    </p>
                    <p>
                        We believe that good health management should be accessible to everyone, regardless of technical expertise.
                        That's why we've made FamilyMedManager intuitive enough for grandparents to use, yet powerful enough to handle
                        complex medication regimens for large families.
                    </p>
                </section>

                {/* Contact CTA */}
                <section className="cta-section">
                    <div className="cta-content">
                        <h2>Questions About Our Mission?</h2>
                        <p>We'd love to hear from you and learn about your family's medication management needs.</p>
                        <button type="button" className="btn-primary">Get in Touch</button>
                    </div>
                </section>
            </div>
        </Layout>
    )
}
