import Layout from '../components/Layout'
import { useState } from 'react'

export default function Contact() {
    const [formData, setFormData] = useState({
        name: '',
        email: '',
        subject: '',
        message: ''
    })

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target
        setFormData(prev => ({
            ...prev,
            [name]: value
        }))
    }

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault()
        // Handle form submission here
        console.log('Form submitted:', formData)
        alert('Thank you for your message! We\'ll get back to you soon.')
        setFormData({ name: '', email: '', subject: '', message: '' })
    }

    return (
        <Layout>
            <div className="contact-page">
                {/* Hero Section */}
                <section className="page-hero">
                    <h1>Contact & Support</h1>
                    <p className="hero-subtitle">
                        We're here to help you get the most out of FamilyMedManager
                    </p>
                </section>

                <div className="contact-content">
                    {/* Contact Form */}
                    <section className="contact-form-section">
                        <h2>Get in Touch</h2>
                        <p>Have a question, suggestion, or need support? We'd love to hear from you.</p>

                        <form className="contact-form" onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label htmlFor="name">Full Name *</label>
                                <input
                                    type="text"
                                    id="name"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleInputChange}
                                    required
                                    className="form-input"
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="email">Email Address *</label>
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleInputChange}
                                    required
                                    className="form-input"
                                />
                            </div>

                            <div className="form-group">
                                <label htmlFor="subject">Subject *</label>
                                <select
                                    id="subject"
                                    name="subject"
                                    value={formData.subject}
                                    onChange={handleInputChange}
                                    required
                                    className="form-input"
                                >
                                    <option value="">Select a subject</option>
                                    <option value="general">General Inquiry</option>
                                    <option value="support">Technical Support</option>
                                    <option value="feature">Feature Request</option>
                                    <option value="bug">Bug Report</option>
                                    <option value="partnership">Partnership</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <div className="form-group">
                                <label htmlFor="message">Message *</label>
                                <textarea
                                    id="message"
                                    name="message"
                                    value={formData.message}
                                    onChange={handleInputChange}
                                    required
                                    rows={6}
                                    className="form-input"
                                    placeholder="Please describe your question or issue in detail..."
                                />
                            </div>

                            <button type="submit" className="btn-primary">Send Message</button>
                        </form>
                    </section>

                    {/* Contact Information */}
                    <section className="contact-info-section">
                        <h2>Other Ways to Reach Us</h2>

                        <div className="contact-methods">
                            <div className="contact-method">
                                <h3>📧 Email Support</h3>
                                <p>For general inquiries and support:</p>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>

                            <div className="contact-method">
                                <h3>🚨 Emergency Support</h3>
                                <p>For urgent technical issues:</p>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                                <small>Response within 4 hours during business days</small>
                            </div>

                            <div className="contact-method">
                                <h3>💼 Business Inquiries</h3>
                                <p>For partnerships and business opportunities:</p>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </div>
                        </div>
                    </section>
                </div>

                {/* FAQ Section */}
                <section className="faq-section">
                    <h2>Frequently Asked Questions</h2>
                    <div className="faq-grid">
                        <div className="faq-item">
                            <h4>How secure is my family's health data?</h4>
                            <p>
                                Your data is stored locally on your device using SQLite database with offline-first architecture.
                                We don't store your personal health information on our servers.
                            </p>
                        </div>

                        <div className="faq-item">
                            <h4>Can I use FamilyMedManager on multiple devices?</h4>
                            <p>
                                Yes! FamilyMedManager works on iOS, Android, and Web platforms. Data synchronization
                                features are available to keep your information consistent across devices.
                            </p>
                        </div>

                        <div className="faq-item">
                            <h4>Is there a cost to use FamilyMedManager?</h4>
                            <p>
                                FamilyMedManager offers both free and premium tiers. The free version includes core
                                medication tracking features, while premium unlocks AI-powered recommendations and advanced analytics.
                            </p>
                        </div>

                        <div className="faq-item">
                            <h4>How does the AI search feature work?</h4>
                            <p>
                                Our AI feature analyzes your symptoms and available medication inventory to provide
                                personalized recommendations and first aid guidance, powered by OpenAI's advanced language models.
                            </p>
                        </div>
                    </div>
                </section>

                {/* Support Hours */}
                <section className="support-hours">
                    <h2>Support Hours</h2>
                    <div className="hours-info">
                        <p><strong>Email Support:</strong> Monday - Friday, 9:00 AM - 6:00 PM EST</p>
                        <p><strong>Emergency Support:</strong> Available 24/7 for critical issues</p>
                        <p><strong>Response Time:</strong> We typically respond within 24 hours during business days</p>
                    </div>
                </section>
            </div>
        </Layout>
    )
}
