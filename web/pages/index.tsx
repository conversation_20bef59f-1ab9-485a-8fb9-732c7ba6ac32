import Layout from '../components/Layout'

export default function Home() {
    return (
        <Layout>
            {/* Hero Section */}
            <section className="hero">
                <div className="hero-content">
                    <h1 className="hero-title">FamilyMedManager 💊</h1>
                    <p className="hero-subtitle">
                        A comprehensive family medication management solution. Track medications, manage dosages,
                        monitor inventory, and ensure your family's health needs are met efficiently.
                    </p>
                    <div className="hero-buttons">
                        <button type="button" className="btn-primary">Get Started</button>
                        <button type="button" className="btn-secondary">Learn More</button>
                    </div>
                </div>
            </section>

            {/* Features Section */}
            <section className="features-section">
                <div className="section-header">
                    <h2>Comprehensive Family Health Management</h2>
                    <p className="section-subtitle">Everything you need to keep your family's medications organized and accessible</p>
                </div>

                <div className="features-grid">
                    <div className="feature-card">
                        <div className="feature-icon">👨‍👩‍👧‍👦</div>
                        <h3>Family Management</h3>
                        <p>Add and manage family members (adults and children) with personalized profiles and medication tracking.</p>
                    </div>

                    <div className="feature-card">
                        <div className="feature-icon">💊</div>
                        <h3>Medication Tracking</h3>
                        <p>Comprehensive medication database with detailed information, dosages, and administration schedules.</p>
                    </div>

                    <div className="feature-card">
                        <div className="feature-icon">📅</div>
                        <h3>Dosage Scheduling</h3>
                        <p>Set up and track medication schedules with smart reminders to never miss a dose.</p>
                    </div>

                    <div className="feature-card">
                        <div className="feature-icon">📦</div>
                        <h3>Inventory Management</h3>
                        <p>Monitor medication stock levels and expiration dates with automated refill reminders.</p>
                    </div>

                    <div className="feature-card">
                        <div className="feature-icon">🔍</div>
                        <h3>AI-Powered Search</h3>
                        <p>Get intelligent medication recommendations and first aid guidance based on symptoms and available inventory.</p>
                    </div>

                    <div className="feature-card">
                        <div className="feature-icon">📱</div>
                        <h3>Cross-Platform</h3>
                        <p>Works seamlessly on iOS, Android, and Web with synchronized data across all devices.</p>
                    </div>
                </div>
            </section>

            {/* Benefits Section */}
            <section className="benefits-section">
                <div className="section-header">
                    <h2>Why Choose FamilyMedManager?</h2>
                </div>

                <div className="benefits-grid">
                    <div className="benefit-item">
                        <h4>🔒 Secure & Private</h4>
                        <p>Your family's health data is stored securely with local SQLite database and offline functionality.</p>
                    </div>

                    <div className="benefit-item">
                        <h4>🎯 Smart Recommendations</h4>
                        <p>AI-powered search provides personalized medication recommendations and first aid guidance.</p>
                    </div>

                    <div className="benefit-item">
                        <h4>⚡ Always Available</h4>
                        <p>Offline functionality ensures your medication information is always accessible when you need it.</p>
                    </div>

                    <div className="benefit-item">
                        <h4>👨‍⚕️ Healthcare Ready</h4>
                        <p>Organized medication records ready to share with healthcare providers and emergency responders.</p>
                    </div>
                </div>
            </section>

            {/* CTA Section */}
            <section className="cta-section">
                <div className="cta-content">
                    <h2>Ready to Take Control of Your Family's Health?</h2>
                    <p>Join thousands of families who trust FamilyMedManager to keep their medication information organized and accessible.</p>
                    <div className="cta-buttons">
                        <button type="button" className="btn-primary large">Download Now</button>
                        <button type="button" className="btn-outline large">View Demo</button>
                    </div>
                </div>
            </section>
        </Layout>
    )
}
