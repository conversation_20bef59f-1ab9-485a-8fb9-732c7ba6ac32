import Layout from '../components/Layout'

export default function Privacy() {
    return (
        <Layout>
            <div className="privacy-page">
                {/* Hero Section */}
                <section className="page-hero">
                    <h1>Privacy Policy</h1>
                    <p className="hero-subtitle">
                        Your privacy and the security of your family's health information is our top priority
                    </p>
                    <p className="last-updated">Last updated: October 4, 2025</p>
                </section>

                {/* Introduction */}
                <section className="content-section">
                    <h2>Introduction</h2>
                    <p>
                        At FamilyMedManager, we understand that your family's health information is deeply personal and sensitive.
                        This Privacy Policy explains how we collect, use, protect, and handle your information when you use our
                        medication management application and related services.
                    </p>
                    <p>
                        We are committed to maintaining the highest standards of privacy and security, employing a privacy-by-design
                        approach that prioritizes local data storage and minimal data collection.
                    </p>
                </section>

                {/* Information We Collect */}
                <section className="content-section">
                    <h2>Information We Collect</h2>

                    <h3>Personal Health Information</h3>
                    <p>When you use FamilyMedManager, you may provide:</p>
                    <ul>
                        <li>Family member names and basic demographic information (age categories: adult/child)</li>
                        <li>Medication names, dosages, and administration schedules</li>
                        <li>Medication inventory levels and expiration dates</li>
                        <li>Symptom information when using our AI search feature</li>
                        <li>Notes and additional context related to medication management</li>
                    </ul>

                    <h3>Technical Information</h3>
                    <p>We may automatically collect:</p>
                    <ul>
                        <li>Device information (operating system, app version)</li>
                        <li>Usage analytics (feature usage, error logs)</li>
                        <li>Performance data to improve app functionality</li>
                    </ul>

                    <h3>AI Search Interactions</h3>
                    <p>When you use our AI-powered search feature:</p>
                    <ul>
                        <li>Symptom descriptions and severity assessments</li>
                        <li>Patient type information (adult/child)</li>
                        <li>Additional context you provide</li>
                        <li>Search interaction logs (stored locally for analytics)</li>
                    </ul>
                </section>

                {/* How We Use Information */}
                <section className="content-section">
                    <h2>How We Use Your Information</h2>
                    <p>We use your information solely to provide and improve our services:</p>

                    <h3>Core Functionality</h3>
                    <ul>
                        <li>Medication tracking and schedule management</li>
                        <li>Inventory monitoring and refill reminders</li>
                        <li>Family member profile management</li>
                        <li>Data synchronization across your devices</li>
                    </ul>

                    <h3>AI-Powered Features</h3>
                    <ul>
                        <li>Providing personalized medication recommendations</li>
                        <li>Offering first aid guidance based on symptoms</li>
                        <li>Analyzing your medication inventory for relevant suggestions</li>
                    </ul>

                    <h3>Service Improvement</h3>
                    <ul>
                        <li>Analyzing usage patterns to enhance user experience</li>
                        <li>Identifying and fixing technical issues</li>
                        <li>Developing new features based on user needs</li>
                    </ul>
                </section>

                {/* Data Storage and Security */}
                <section className="content-section">
                    <h2>Data Storage and Security</h2>

                    <h3>Local-First Architecture</h3>
                    <p>
                        FamilyMedManager employs a local-first data storage approach. Your personal health information is
                        primarily stored on your device using a secure SQLite database. This means:
                    </p>
                    <ul>
                        <li>Your data remains on your device and under your control</li>
                        <li>The app functions offline without requiring internet connectivity</li>
                        <li>We cannot access your personal health data stored locally</li>
                    </ul>

                    <h3>Security Measures</h3>
                    <p>We implement multiple layers of security:</p>
                    <ul>
                        <li>Encrypted local database storage</li>
                        <li>Secure API communications using HTTPS</li>
                        <li>Regular security audits and updates</li>
                        <li>Access controls and authentication mechanisms</li>
                    </ul>

                    <h3>Cloud Services</h3>
                    <p>
                        When using AI search features, symptom and medication data may be temporarily processed by
                        OpenAI's services to generate recommendations. This data is:
                    </p>
                    <ul>
                        <li>Transmitted securely using encryption</li>
                        <li>Not stored permanently by third-party services</li>
                        <li>Anonymized and contains no personally identifiable information</li>
                    </ul>
                </section>

                {/* Data Sharing */}
                <section className="content-section">
                    <h2>Data Sharing and Disclosure</h2>
                    <p>We do not sell, rent, or share your personal health information with third parties, except:</p>

                    <h3>Service Providers</h3>
                    <ul>
                        <li>OpenAI for AI-powered search functionality (anonymized data only)</li>
                        <li>Cloud infrastructure providers for app delivery and updates</li>
                        <li>Analytics services for app performance monitoring (aggregated data only)</li>
                    </ul>

                    <h3>Legal Requirements</h3>
                    <p>We may disclose information if required by law or to:</p>
                    <ul>
                        <li>Comply with legal processes or government requests</li>
                        <li>Protect our rights, property, or safety</li>
                        <li>Prevent fraud or security threats</li>
                    </ul>
                </section>

                {/* Your Rights */}
                <section className="content-section">
                    <h2>Your Privacy Rights</h2>
                    <p>You have the following rights regarding your data:</p>

                    <h3>Access and Control</h3>
                    <ul>
                        <li>View all data stored in the app through the interface</li>
                        <li>Export your data in a portable format</li>
                        <li>Delete specific medications, family members, or all data</li>
                        <li>Control which features and permissions the app uses</li>
                    </ul>

                    <h3>Data Portability</h3>
                    <ul>
                        <li>Export your medication and family data</li>
                        <li>Transfer data between devices</li>
                        <li>Create backups of your information</li>
                    </ul>

                    <h3>Deletion Rights</h3>
                    <ul>
                        <li>Delete your account and all associated data</li>
                        <li>Remove specific information or family members</li>
                        <li>Clear search history and interaction logs</li>
                    </ul>
                </section>

                {/* Children's Privacy */}
                <section className="content-section">
                    <h2>Children's Privacy</h2>
                    <p>
                        FamilyMedManager is designed for family use and may include information about children's medications.
                        We take special care to protect children's privacy:
                    </p>
                    <ul>
                        <li>We do not knowingly collect personal information directly from children under 13</li>
                        <li>All children's information is managed by parent or guardian accounts</li>
                        <li>Children's data receives the same security protections as adult data</li>
                        <li>Parents can review and delete their children's information at any time</li>
                    </ul>
                </section>

                {/* Updates to Privacy Policy */}
                <section className="content-section">
                    <h2>Updates to This Privacy Policy</h2>
                    <p>
                        We may update this Privacy Policy from time to time to reflect changes in our practices or legal requirements.
                        When we make significant changes, we will:
                    </p>
                    <ul>
                        <li>Notify you through the app or via email</li>
                        <li>Update the "Last updated" date at the top of this policy</li>
                        <li>Provide a summary of key changes</li>
                        <li>Give you the opportunity to review and accept the updated policy</li>
                    </ul>
                </section>

                {/* Contact Information */}
                <section className="content-section">
                    <h2>Contact Us</h2>
                    <p>
                        If you have questions about this Privacy Policy or our privacy practices, please contact us:
                    </p>
                    <div className="contact-info">
                        <p><strong>Email:</strong> <a href="mailto:<EMAIL>"><EMAIL></a></p>
                        <p><strong>Subject Line:</strong> Privacy Policy Inquiry</p>
                        <p><strong>Response Time:</strong> We will respond to privacy inquiries within 48 hours</p>
                    </div>
                </section>

                {/* Effective Date */}
                <section className="effective-date">
                    <p>
                        <strong>This Privacy Policy is effective as of October 4, 2025, and applies to all users of FamilyMedManager.</strong>
                    </p>
                </section>
            </div>
        </Layout>
    )
}
