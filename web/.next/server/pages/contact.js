/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/contact";
exports.ids = ["pages/contact"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%2Fcontact.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%2Fcontact.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/contact.tsx */ \"./pages/contact.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/contact\",\n        pathname: \"/contact\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%2Fcontact.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Layout: () => (/* binding */ Layout),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Layout = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"site-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"site-logo\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"FamilyMedManager \\uD83D\\uDC8A\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"nav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/about\",\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/contact\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    children: \"Privacy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                lineNumber: 7,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n        lineNumber: 6,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 12\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBOEI7QUFHZixTQUFTQSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzFELHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNuQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhbWlseS1tZWQtbWFuYWdlci13ZWIvLi9wYWdlcy9fYXBwLnRzeD8yZmJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICAgIHJldHVybiA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG59XG4iXSwibmFtZXMiOlsiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/contact.tsx":
/*!***************************!*\
  !*** ./pages/contact.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.tsx\");\n\n\nfunction Contact() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"contact-page\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"page-hero\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"Contact & Support\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hero-subtitle\",\n                            children: \"We're here to help you get the most out of FamilyMedManager\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"contact-info-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Get in Touch\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Have a question, suggestion, or need support? We'd love to hear from you.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"contact-methods\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-method\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"\\uD83D\\uDCE7 General Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"For all inquiries, support, and feedback:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-method\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"\\uD83D\\uDEA8 Technical Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"For urgent technical issues and bug reports:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                            children: 'Please include \"URGENT\" in the subject line for priority support'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-method\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"\\uD83D\\uDCA1 Feature Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Have an idea to improve FamilyMedManager?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                            children: \"We love hearing from our users about new features!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"faq-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"faq-grid\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How secure is my family's health data?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Your data is stored locally on your device using SQLite database with offline-first architecture. We don't store your personal health information on our servers.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I use FamilyMedManager on multiple devices?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! FamilyMedManager works on iOS, Android, and Web platforms. Data synchronization features are available to keep your information consistent across devices.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Is there a cost to use FamilyMedManager?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"FamilyMedManager offers both free and premium tiers. The free version includes core medication tracking features, while premium unlocks AI-powered recommendations and advanced analytics.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How does the AI search feature work?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Our AI feature analyzes your symptoms and available medication inventory to provide personalized recommendations and first aid guidance, powered by OpenAI's advanced language models.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I track medications for children and adults separately?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! FamilyMedManager allows you to create separate profiles for adults and children, with age-appropriate medication tracking and dosage recommendations for each family member.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How do medication reminders work?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"The app sends smart notifications based on your medication schedules. You can set custom reminder times, snooze options, and track when doses are taken to maintain accurate records.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"What happens if I lose my phone or device?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Since data is stored locally, we recommend regular backups. The app includes export functionality to save your medication data, and you can restore it on a new device.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I share medication information with my doctor?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Absolutely! FamilyMedManager allows you to export comprehensive medication reports that you can share with healthcare providers, including dosages, schedules, and adherence history.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How accurate is the inventory tracking?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"The inventory system tracks your medication usage based on scheduled doses and manual updates. It provides estimates for refill dates and low-stock alerts to help you stay prepared.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Does the app work without internet connection?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! FamilyMedManager is designed with offline-first architecture. All core features work without internet, though AI search requires connectivity to provide recommendations.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I set up medication schedules for complex regimens?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"The app supports flexible scheduling including multiple daily doses, weekly medications, as-needed prescriptions, and custom intervals to accommodate any medication regimen.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Is my medication data encrypted?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes, all medication data is encrypted both in storage and during any data transfers. We use industry-standard encryption to protect your sensitive health information.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How do I add a new family member?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: 'Simply navigate to the family management section and tap \"Add Family Member.\" You can create profiles for adults or children and assign medications to specific individuals.'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I track over-the-counter medications and supplements?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! The app tracks all types of medications including prescriptions, over-the-counter drugs, vitamins, supplements, and herbal remedies with the same level of detail.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"What should I do in case of a medical emergency?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"FamilyMedManager provides quick access to your medication list for emergency responders, but always call emergency services first. The app includes emergency contact features for critical situations.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How often should I update my medication information?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Update your information whenever there are changes to prescriptions, dosages, or schedules. Regular updates ensure accurate tracking and reliable AI recommendations.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I customize notification sounds and timing?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! You can personalize reminder notifications with custom sounds, vibration patterns, and timing preferences to fit your daily routine and ensure you never miss a dose.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Is there a limit to how many medications I can track?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No, there's no limit! You can track as many medications as needed for your entire family. The app is designed to handle complex medication regimens for households of any size.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"support-hours\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Support Hours\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hours-info\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Email Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Monday - Friday, 9:00 AM - 6:00 PM EST\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Emergency Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Available 24/7 for critical issues\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Response Time:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" We typically respond within 24 hours during business days\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n            lineNumber: 7,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n        lineNumber: 6,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9jb250YWN0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUF5QztBQUUxQixTQUFTQztJQUVwQixxQkFDSSw4REFBQ0QsMERBQU1BO2tCQUNILDRFQUFDRTtZQUFJQyxXQUFVOzs4QkFFWCw4REFBQ0M7b0JBQVFELFdBQVU7O3NDQUNmLDhEQUFDRTtzQ0FBRzs7Ozs7O3NDQUNKLDhEQUFDQzs0QkFBRUgsV0FBVTtzQ0FBZ0I7Ozs7Ozs7Ozs7Ozs4QkFNakMsOERBQUNDO29CQUFRRCxXQUFVOztzQ0FDZiw4REFBQ0k7c0NBQUc7Ozs7OztzQ0FDSiw4REFBQ0Q7c0NBQUU7Ozs7OztzQ0FFSCw4REFBQ0o7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDRDtvQ0FBSUMsV0FBVTs7c0RBQ1gsOERBQUNLO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNGO3NEQUFFOzs7Ozs7c0RBQ0gsOERBQUNHOzRDQUFFQyxNQUFLO3NEQUFnQzs7Ozs7Ozs7Ozs7OzhDQUc1Qyw4REFBQ1I7b0NBQUlDLFdBQVU7O3NEQUNYLDhEQUFDSztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDRjtzREFBRTs7Ozs7O3NEQUNILDhEQUFDRzs0Q0FBRUMsTUFBSztzREFBZ0M7Ozs7OztzREFDeEMsOERBQUNDO3NEQUFNOzs7Ozs7Ozs7Ozs7OENBR1gsOERBQUNUO29DQUFJQyxXQUFVOztzREFDWCw4REFBQ0s7c0RBQUc7Ozs7OztzREFDSiw4REFBQ0Y7c0RBQUU7Ozs7OztzREFDSCw4REFBQ0c7NENBQUVDLE1BQUs7c0RBQWdDOzs7Ozs7c0RBQ3hDLDhEQUFDQztzREFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzhCQU1uQiw4REFBQ1A7b0JBQVFELFdBQVU7O3NDQUNmLDhEQUFDSTtzQ0FBRzs7Ozs7O3NDQUNKLDhEQUFDTDs0QkFBSUMsV0FBVTs7OENBQ1gsOERBQUNEO29DQUFJQyxXQUFVOztzREFDWCw4REFBQ1M7c0RBQUc7Ozs7OztzREFDSiw4REFBQ047c0RBQUU7Ozs7Ozs7Ozs7Ozs4Q0FNUCw4REFBQ0o7b0NBQUlDLFdBQVU7O3NEQUNYLDhEQUFDUztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDTjtzREFBRTs7Ozs7Ozs7Ozs7OzhDQU1QLDhEQUFDSjtvQ0FBSUMsV0FBVTs7c0RBQ1gsOERBQUNTO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNOO3NEQUFFOzs7Ozs7Ozs7Ozs7OENBTVAsOERBQUNKO29DQUFJQyxXQUFVOztzREFDWCw4REFBQ1M7c0RBQUc7Ozs7OztzREFDSiw4REFBQ047c0RBQUU7Ozs7Ozs7Ozs7Ozs4Q0FNUCw4REFBQ0o7b0NBQUlDLFdBQVU7O3NEQUNYLDhEQUFDUztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDTjtzREFBRTs7Ozs7Ozs7Ozs7OzhDQU1QLDhEQUFDSjtvQ0FBSUMsV0FBVTs7c0RBQ1gsOERBQUNTO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNOO3NEQUFFOzs7Ozs7Ozs7Ozs7OENBTVAsOERBQUNKO29DQUFJQyxXQUFVOztzREFDWCw4REFBQ1M7c0RBQUc7Ozs7OztzREFDSiw4REFBQ047c0RBQUU7Ozs7Ozs7Ozs7Ozs4Q0FNUCw4REFBQ0o7b0NBQUlDLFdBQVU7O3NEQUNYLDhEQUFDUztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDTjtzREFBRTs7Ozs7Ozs7Ozs7OzhDQU1QLDhEQUFDSjtvQ0FBSUMsV0FBVTs7c0RBQ1gsOERBQUNTO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNOO3NEQUFFOzs7Ozs7Ozs7Ozs7OENBTVAsOERBQUNKO29DQUFJQyxXQUFVOztzREFDWCw4REFBQ1M7c0RBQUc7Ozs7OztzREFDSiw4REFBQ047c0RBQUU7Ozs7Ozs7Ozs7Ozs4Q0FNUCw4REFBQ0o7b0NBQUlDLFdBQVU7O3NEQUNYLDhEQUFDUztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDTjtzREFBRTs7Ozs7Ozs7Ozs7OzhDQU1QLDhEQUFDSjtvQ0FBSUMsV0FBVTs7c0RBQ1gsOERBQUNTO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNOO3NEQUFFOzs7Ozs7Ozs7Ozs7OENBTVAsOERBQUNKO29DQUFJQyxXQUFVOztzREFDWCw4REFBQ1M7c0RBQUc7Ozs7OztzREFDSiw4REFBQ047c0RBQUU7Ozs7Ozs7Ozs7Ozs4Q0FNUCw4REFBQ0o7b0NBQUlDLFdBQVU7O3NEQUNYLDhEQUFDUztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDTjtzREFBRTs7Ozs7Ozs7Ozs7OzhDQU1QLDhEQUFDSjtvQ0FBSUMsV0FBVTs7c0RBQ1gsOERBQUNTO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNOO3NEQUFFOzs7Ozs7Ozs7Ozs7OENBTVAsOERBQUNKO29DQUFJQyxXQUFVOztzREFDWCw4REFBQ1M7c0RBQUc7Ozs7OztzREFDSiw4REFBQ047c0RBQUU7Ozs7Ozs7Ozs7Ozs4Q0FNUCw4REFBQ0o7b0NBQUlDLFdBQVU7O3NEQUNYLDhEQUFDUztzREFBRzs7Ozs7O3NEQUNKLDhEQUFDTjtzREFBRTs7Ozs7Ozs7Ozs7OzhDQU1QLDhEQUFDSjtvQ0FBSUMsV0FBVTs7c0RBQ1gsOERBQUNTO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNOO3NEQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBU2YsOERBQUNGO29CQUFRRCxXQUFVOztzQ0FDZiw4REFBQ0k7c0NBQUc7Ozs7OztzQ0FDSiw4REFBQ0w7NEJBQUlDLFdBQVU7OzhDQUNYLDhEQUFDRzs7c0RBQUUsOERBQUNPO3NEQUFPOzs7Ozs7d0NBQXVCOzs7Ozs7OzhDQUNsQyw4REFBQ1A7O3NEQUFFLDhEQUFDTztzREFBTzs7Ozs7O3dDQUEyQjs7Ozs7Ozs4Q0FDdEMsOERBQUNQOztzREFBRSw4REFBQ087c0RBQU87Ozs7Ozt3Q0FBdUI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU0xRCIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhbWlseS1tZWQtbWFuYWdlci13ZWIvLi9wYWdlcy9jb250YWN0LnRzeD8yNGRiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBMYXlvdXQgZnJvbSAnLi4vY29tcG9uZW50cy9MYXlvdXQnXG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENvbnRhY3QoKSB7XG5cbiAgICByZXR1cm4gKFxuICAgICAgICA8TGF5b3V0PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWN0LXBhZ2VcIj5cbiAgICAgICAgICAgICAgICB7LyogSGVybyBTZWN0aW9uICovfVxuICAgICAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInBhZ2UtaGVyb1wiPlxuICAgICAgICAgICAgICAgICAgICA8aDE+Q29udGFjdCAmIFN1cHBvcnQ8L2gxPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJoZXJvLXN1YnRpdGxlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICBXZSdyZSBoZXJlIHRvIGhlbHAgeW91IGdldCB0aGUgbW9zdCBvdXQgb2YgRmFtaWx5TWVkTWFuYWdlclxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgICAgICAgICAgey8qIENvbnRhY3QgSW5mb3JtYXRpb24gKi99XG4gICAgICAgICAgICAgICAgPHNlY3Rpb24gY2xhc3NOYW1lPVwiY29udGFjdC1pbmZvLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICAgICAgPGgyPkdldCBpbiBUb3VjaDwvaDI+XG4gICAgICAgICAgICAgICAgICAgIDxwPkhhdmUgYSBxdWVzdGlvbiwgc3VnZ2VzdGlvbiwgb3IgbmVlZCBzdXBwb3J0PyBXZSdkIGxvdmUgdG8gaGVhciBmcm9tIHlvdS48L3A+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWN0LW1ldGhvZHNcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFjdC1tZXRob2RcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDM+8J+TpyBHZW5lcmFsIENvbnRhY3Q8L2gzPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPkZvciBhbGwgaW5xdWlyaWVzLCBzdXBwb3J0LCBhbmQgZmVlZGJhY2s6PC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxhIGhyZWY9XCJtYWlsdG86amFuLmthbWFyYWpAaWNsb3VkLmNvbVwiPmphbi5rYW1hcmFqQGljbG91ZC5jb208L2E+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjb250YWN0LW1ldGhvZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoMz7wn5qoIFRlY2huaWNhbCBTdXBwb3J0PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5Gb3IgdXJnZW50IHRlY2huaWNhbCBpc3N1ZXMgYW5kIGJ1ZyByZXBvcnRzOjwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YSBocmVmPVwibWFpbHRvOmphbi5rYW1hcmFqQGljbG91ZC5jb21cIj5qYW4ua2FtYXJhakBpY2xvdWQuY29tPC9hPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzbWFsbD5QbGVhc2UgaW5jbHVkZSBcIlVSR0VOVFwiIGluIHRoZSBzdWJqZWN0IGxpbmUgZm9yIHByaW9yaXR5IHN1cHBvcnQ8L3NtYWxsPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29udGFjdC1tZXRob2RcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDM+8J+SoSBGZWF0dXJlIFJlcXVlc3RzPC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5IYXZlIGFuIGlkZWEgdG8gaW1wcm92ZSBGYW1pbHlNZWRNYW5hZ2VyPzwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8YSBocmVmPVwibWFpbHRvOmphbi5rYW1hcmFqQGljbG91ZC5jb21cIj5qYW4ua2FtYXJhakBpY2xvdWQuY29tPC9hPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzbWFsbD5XZSBsb3ZlIGhlYXJpbmcgZnJvbSBvdXIgdXNlcnMgYWJvdXQgbmV3IGZlYXR1cmVzITwvc21hbGw+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgICAgICAgICAgey8qIEZBUSBTZWN0aW9uICovfVxuICAgICAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImZhcS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMj5GcmVxdWVudGx5IEFza2VkIFF1ZXN0aW9uczwvaDI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmFxLWdyaWRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmFxLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQ+SG93IHNlY3VyZSBpcyBteSBmYW1pbHkncyBoZWFsdGggZGF0YT88L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBZb3VyIGRhdGEgaXMgc3RvcmVkIGxvY2FsbHkgb24geW91ciBkZXZpY2UgdXNpbmcgU1FMaXRlIGRhdGFiYXNlIHdpdGggb2ZmbGluZS1maXJzdCBhcmNoaXRlY3R1cmUuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFdlIGRvbid0IHN0b3JlIHlvdXIgcGVyc29uYWwgaGVhbHRoIGluZm9ybWF0aW9uIG9uIG91ciBzZXJ2ZXJzLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZhcS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0PkNhbiBJIHVzZSBGYW1pbHlNZWRNYW5hZ2VyIG9uIG11bHRpcGxlIGRldmljZXM/PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWWVzISBGYW1pbHlNZWRNYW5hZ2VyIHdvcmtzIG9uIGlPUywgQW5kcm9pZCwgYW5kIFdlYiBwbGF0Zm9ybXMuIERhdGEgc3luY2hyb25pemF0aW9uXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZlYXR1cmVzIGFyZSBhdmFpbGFibGUgdG8ga2VlcCB5b3VyIGluZm9ybWF0aW9uIGNvbnNpc3RlbnQgYWNyb3NzIGRldmljZXMuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmFxLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQ+SXMgdGhlcmUgYSBjb3N0IHRvIHVzZSBGYW1pbHlNZWRNYW5hZ2VyPzwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEZhbWlseU1lZE1hbmFnZXIgb2ZmZXJzIGJvdGggZnJlZSBhbmQgcHJlbWl1bSB0aWVycy4gVGhlIGZyZWUgdmVyc2lvbiBpbmNsdWRlcyBjb3JlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1lZGljYXRpb24gdHJhY2tpbmcgZmVhdHVyZXMsIHdoaWxlIHByZW1pdW0gdW5sb2NrcyBBSS1wb3dlcmVkIHJlY29tbWVuZGF0aW9ucyBhbmQgYWR2YW5jZWQgYW5hbHl0aWNzLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZhcS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0PkhvdyBkb2VzIHRoZSBBSSBzZWFyY2ggZmVhdHVyZSB3b3JrPzwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIE91ciBBSSBmZWF0dXJlIGFuYWx5emVzIHlvdXIgc3ltcHRvbXMgYW5kIGF2YWlsYWJsZSBtZWRpY2F0aW9uIGludmVudG9yeSB0byBwcm92aWRlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHBlcnNvbmFsaXplZCByZWNvbW1lbmRhdGlvbnMgYW5kIGZpcnN0IGFpZCBndWlkYW5jZSwgcG93ZXJlZCBieSBPcGVuQUkncyBhZHZhbmNlZCBsYW5ndWFnZSBtb2RlbHMuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmFxLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQ+Q2FuIEkgdHJhY2sgbWVkaWNhdGlvbnMgZm9yIGNoaWxkcmVuIGFuZCBhZHVsdHMgc2VwYXJhdGVseT88L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBZZXMhIEZhbWlseU1lZE1hbmFnZXIgYWxsb3dzIHlvdSB0byBjcmVhdGUgc2VwYXJhdGUgcHJvZmlsZXMgZm9yIGFkdWx0cyBhbmQgY2hpbGRyZW4sXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHdpdGggYWdlLWFwcHJvcHJpYXRlIG1lZGljYXRpb24gdHJhY2tpbmcgYW5kIGRvc2FnZSByZWNvbW1lbmRhdGlvbnMgZm9yIGVhY2ggZmFtaWx5IG1lbWJlci5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmYXEtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoND5Ib3cgZG8gbWVkaWNhdGlvbiByZW1pbmRlcnMgd29yaz88L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUaGUgYXBwIHNlbmRzIHNtYXJ0IG5vdGlmaWNhdGlvbnMgYmFzZWQgb24geW91ciBtZWRpY2F0aW9uIHNjaGVkdWxlcy4gWW91IGNhbiBzZXQgY3VzdG9tXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHJlbWluZGVyIHRpbWVzLCBzbm9vemUgb3B0aW9ucywgYW5kIHRyYWNrIHdoZW4gZG9zZXMgYXJlIHRha2VuIHRvIG1haW50YWluIGFjY3VyYXRlIHJlY29yZHMuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmFxLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQ+V2hhdCBoYXBwZW5zIGlmIEkgbG9zZSBteSBwaG9uZSBvciBkZXZpY2U/PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgU2luY2UgZGF0YSBpcyBzdG9yZWQgbG9jYWxseSwgd2UgcmVjb21tZW5kIHJlZ3VsYXIgYmFja3Vwcy4gVGhlIGFwcCBpbmNsdWRlcyBleHBvcnQgZnVuY3Rpb25hbGl0eVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB0byBzYXZlIHlvdXIgbWVkaWNhdGlvbiBkYXRhLCBhbmQgeW91IGNhbiByZXN0b3JlIGl0IG9uIGEgbmV3IGRldmljZS5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmYXEtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoND5DYW4gSSBzaGFyZSBtZWRpY2F0aW9uIGluZm9ybWF0aW9uIHdpdGggbXkgZG9jdG9yPzwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFic29sdXRlbHkhIEZhbWlseU1lZE1hbmFnZXIgYWxsb3dzIHlvdSB0byBleHBvcnQgY29tcHJlaGVuc2l2ZSBtZWRpY2F0aW9uIHJlcG9ydHMgdGhhdCB5b3UgY2FuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHNoYXJlIHdpdGggaGVhbHRoY2FyZSBwcm92aWRlcnMsIGluY2x1ZGluZyBkb3NhZ2VzLCBzY2hlZHVsZXMsIGFuZCBhZGhlcmVuY2UgaGlzdG9yeS5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmYXEtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoND5Ib3cgYWNjdXJhdGUgaXMgdGhlIGludmVudG9yeSB0cmFja2luZz88L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUaGUgaW52ZW50b3J5IHN5c3RlbSB0cmFja3MgeW91ciBtZWRpY2F0aW9uIHVzYWdlIGJhc2VkIG9uIHNjaGVkdWxlZCBkb3NlcyBhbmQgbWFudWFsIHVwZGF0ZXMuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEl0IHByb3ZpZGVzIGVzdGltYXRlcyBmb3IgcmVmaWxsIGRhdGVzIGFuZCBsb3ctc3RvY2sgYWxlcnRzIHRvIGhlbHAgeW91IHN0YXkgcHJlcGFyZWQuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmFxLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQ+RG9lcyB0aGUgYXBwIHdvcmsgd2l0aG91dCBpbnRlcm5ldCBjb25uZWN0aW9uPzwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFllcyEgRmFtaWx5TWVkTWFuYWdlciBpcyBkZXNpZ25lZCB3aXRoIG9mZmxpbmUtZmlyc3QgYXJjaGl0ZWN0dXJlLiBBbGwgY29yZSBmZWF0dXJlcyB3b3JrIHdpdGhvdXRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW50ZXJuZXQsIHRob3VnaCBBSSBzZWFyY2ggcmVxdWlyZXMgY29ubmVjdGl2aXR5IHRvIHByb3ZpZGUgcmVjb21tZW5kYXRpb25zLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZhcS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0PkNhbiBJIHNldCB1cCBtZWRpY2F0aW9uIHNjaGVkdWxlcyBmb3IgY29tcGxleCByZWdpbWVucz88L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUaGUgYXBwIHN1cHBvcnRzIGZsZXhpYmxlIHNjaGVkdWxpbmcgaW5jbHVkaW5nIG11bHRpcGxlIGRhaWx5IGRvc2VzLCB3ZWVrbHkgbWVkaWNhdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGFzLW5lZWRlZCBwcmVzY3JpcHRpb25zLCBhbmQgY3VzdG9tIGludGVydmFscyB0byBhY2NvbW1vZGF0ZSBhbnkgbWVkaWNhdGlvbiByZWdpbWVuLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZhcS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0PklzIG15IG1lZGljYXRpb24gZGF0YSBlbmNyeXB0ZWQ/PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgWWVzLCBhbGwgbWVkaWNhdGlvbiBkYXRhIGlzIGVuY3J5cHRlZCBib3RoIGluIHN0b3JhZ2UgYW5kIGR1cmluZyBhbnkgZGF0YSB0cmFuc2ZlcnMuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFdlIHVzZSBpbmR1c3RyeS1zdGFuZGFyZCBlbmNyeXB0aW9uIHRvIHByb3RlY3QgeW91ciBzZW5zaXRpdmUgaGVhbHRoIGluZm9ybWF0aW9uLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZhcS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0PkhvdyBkbyBJIGFkZCBhIG5ldyBmYW1pbHkgbWVtYmVyPzwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFNpbXBseSBuYXZpZ2F0ZSB0byB0aGUgZmFtaWx5IG1hbmFnZW1lbnQgc2VjdGlvbiBhbmQgdGFwIFwiQWRkIEZhbWlseSBNZW1iZXIuXCIgWW91IGNhblxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjcmVhdGUgcHJvZmlsZXMgZm9yIGFkdWx0cyBvciBjaGlsZHJlbiBhbmQgYXNzaWduIG1lZGljYXRpb25zIHRvIHNwZWNpZmljIGluZGl2aWR1YWxzLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZhcS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0PkNhbiBJIHRyYWNrIG92ZXItdGhlLWNvdW50ZXIgbWVkaWNhdGlvbnMgYW5kIHN1cHBsZW1lbnRzPzwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFllcyEgVGhlIGFwcCB0cmFja3MgYWxsIHR5cGVzIG9mIG1lZGljYXRpb25zIGluY2x1ZGluZyBwcmVzY3JpcHRpb25zLCBvdmVyLXRoZS1jb3VudGVyIGRydWdzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB2aXRhbWlucywgc3VwcGxlbWVudHMsIGFuZCBoZXJiYWwgcmVtZWRpZXMgd2l0aCB0aGUgc2FtZSBsZXZlbCBvZiBkZXRhaWwuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmFxLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQ+V2hhdCBzaG91bGQgSSBkbyBpbiBjYXNlIG9mIGEgbWVkaWNhbCBlbWVyZ2VuY3k/PC9oND5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8cD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgRmFtaWx5TWVkTWFuYWdlciBwcm92aWRlcyBxdWljayBhY2Nlc3MgdG8geW91ciBtZWRpY2F0aW9uIGxpc3QgZm9yIGVtZXJnZW5jeSByZXNwb25kZXJzLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBidXQgYWx3YXlzIGNhbGwgZW1lcmdlbmN5IHNlcnZpY2VzIGZpcnN0LiBUaGUgYXBwIGluY2x1ZGVzIGVtZXJnZW5jeSBjb250YWN0IGZlYXR1cmVzIGZvciBjcml0aWNhbCBzaXR1YXRpb25zLlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvcD5cbiAgICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZhcS1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPGg0PkhvdyBvZnRlbiBzaG91bGQgSSB1cGRhdGUgbXkgbWVkaWNhdGlvbiBpbmZvcm1hdGlvbj88L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBVcGRhdGUgeW91ciBpbmZvcm1hdGlvbiB3aGVuZXZlciB0aGVyZSBhcmUgY2hhbmdlcyB0byBwcmVzY3JpcHRpb25zLCBkb3NhZ2VzLCBvciBzY2hlZHVsZXMuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFJlZ3VsYXIgdXBkYXRlcyBlbnN1cmUgYWNjdXJhdGUgdHJhY2tpbmcgYW5kIHJlbGlhYmxlIEFJIHJlY29tbWVuZGF0aW9ucy5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmYXEtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxoND5DYW4gSSBjdXN0b21pemUgbm90aWZpY2F0aW9uIHNvdW5kcyBhbmQgdGltaW5nPzwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPHA+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIFllcyEgWW91IGNhbiBwZXJzb25hbGl6ZSByZW1pbmRlciBub3RpZmljYXRpb25zIHdpdGggY3VzdG9tIHNvdW5kcywgdmlicmF0aW9uIHBhdHRlcm5zLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBhbmQgdGltaW5nIHByZWZlcmVuY2VzIHRvIGZpdCB5b3VyIGRhaWx5IHJvdXRpbmUgYW5kIGVuc3VyZSB5b3UgbmV2ZXIgbWlzcyBhIGRvc2UuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmFxLWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8aDQ+SXMgdGhlcmUgYSBsaW1pdCB0byBob3cgbWFueSBtZWRpY2F0aW9ucyBJIGNhbiB0cmFjaz88L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxwPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBObywgdGhlcmUncyBubyBsaW1pdCEgWW91IGNhbiB0cmFjayBhcyBtYW55IG1lZGljYXRpb25zIGFzIG5lZWRlZCBmb3IgeW91ciBlbnRpcmUgZmFtaWx5LlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBUaGUgYXBwIGlzIGRlc2lnbmVkIHRvIGhhbmRsZSBjb21wbGV4IG1lZGljYXRpb24gcmVnaW1lbnMgZm9yIGhvdXNlaG9sZHMgb2YgYW55IHNpemUuXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICAgICAgICAgIHsvKiBTdXBwb3J0IEhvdXJzICovfVxuICAgICAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cInN1cHBvcnQtaG91cnNcIj5cbiAgICAgICAgICAgICAgICAgICAgPGgyPlN1cHBvcnQgSG91cnM8L2gyPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhvdXJzLWluZm9cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwPjxzdHJvbmc+RW1haWwgU3VwcG9ydDo8L3N0cm9uZz4gTW9uZGF5IC0gRnJpZGF5LCA5OjAwIEFNIC0gNjowMCBQTSBFU1Q8L3A+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD48c3Ryb25nPkVtZXJnZW5jeSBTdXBwb3J0Ojwvc3Ryb25nPiBBdmFpbGFibGUgMjQvNyBmb3IgY3JpdGljYWwgaXNzdWVzPC9wPlxuICAgICAgICAgICAgICAgICAgICAgICAgPHA+PHN0cm9uZz5SZXNwb25zZSBUaW1lOjwvc3Ryb25nPiBXZSB0eXBpY2FsbHkgcmVzcG9uZCB3aXRoaW4gMjQgaG91cnMgZHVyaW5nIGJ1c2luZXNzIGRheXM8L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvc2VjdGlvbj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0xheW91dD5cbiAgICApXG59XG4iXSwibmFtZXMiOlsiTGF5b3V0IiwiQ29udGFjdCIsImRpdiIsImNsYXNzTmFtZSIsInNlY3Rpb24iLCJoMSIsInAiLCJoMiIsImgzIiwiYSIsImhyZWYiLCJzbWFsbCIsImg0Iiwic3Ryb25nIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/contact.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%2Fcontact.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();