/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/contact";
exports.ids = ["pages/contact"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%2Fcontact.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%2Fcontact.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/contact.tsx */ \"./pages/contact.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/contact\",\n        pathname: \"/contact\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_contact_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%2Fcontact.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Layout: () => (/* binding */ Layout),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Layout = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"site-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"site-logo\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"FamilyMedManager \\uD83D\\uDC8A\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"nav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/about\",\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/contact\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    children: \"Privacy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                lineNumber: 7,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n        lineNumber: 6,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 12\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBOEI7QUFHZixTQUFTQSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzFELHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNuQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhbWlseS1tZWQtbWFuYWdlci13ZWIvLi9wYWdlcy9fYXBwLnRzeD8yZmJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICAgIHJldHVybiA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG59XG4iXSwibmFtZXMiOlsiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/contact.tsx":
/*!***************************!*\
  !*** ./pages/contact.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Contact)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.tsx\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nfunction Contact() {\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        name: \"\",\n        email: \"\",\n        subject: \"\",\n        message: \"\"\n    });\n    const handleInputChange = (e)=>{\n        const { name, value } = e.target;\n        setFormData((prev)=>({\n                ...prev,\n                [name]: value\n            }));\n    };\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        // Handle form submission here\n        console.log(\"Form submitted:\", formData);\n        alert(\"Thank you for your message! We'll get back to you soon.\");\n        setFormData({\n            name: \"\",\n            email: \"\",\n            subject: \"\",\n            message: \"\"\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"contact-page\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"page-hero\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"Contact & Support\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 33,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hero-subtitle\",\n                            children: \"We're here to help you get the most out of FamilyMedManager\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 32,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"contact-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"contact-form-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Get in Touch\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 42,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Have a question, suggestion, or need support? We'd love to hear from you.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 43,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"contact-form\",\n                                    onSubmit: handleSubmit,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"name\",\n                                                    children: \"Full Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 47,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"name\",\n                                                    name: \"name\",\n                                                    value: formData.name,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 48,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 46,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email Address *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 60,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    value: formData.email,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 61,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 59,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"subject\",\n                                                    children: \"Subject *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 73,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"subject\",\n                                                    name: \"subject\",\n                                                    value: formData.subject,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"form-input\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select a subject\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"general\",\n                                                            children: \"General Inquiry\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"support\",\n                                                            children: \"Technical Support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 84,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"feature\",\n                                                            children: \"Feature Request\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 85,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"bug\",\n                                                            children: \"Bug Report\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 86,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"partnership\",\n                                                            children: \"Partnership\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 87,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"other\",\n                                                            children: \"Other\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 88,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 74,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"message\",\n                                                    children: \"Message *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"message\",\n                                                    name: \"message\",\n                                                    value: formData.message,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    rows: 6,\n                                                    className: \"form-input\",\n                                                    placeholder: \"Please describe your question or issue in detail...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"btn-primary\",\n                                            children: \"Send Message\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 45,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 41,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"contact-info-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Other Ways to Reach Us\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-methods\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"contact-method\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: \"\\uD83D\\uDCE7 Email Support\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"For general inquiries and support:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 117,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 118,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"contact-method\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: \"\\uD83D\\uDEA8 Emergency Support\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"For urgent technical issues:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 124,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                    children: \"Response within 4 hours during business days\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 125,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"contact-method\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: \"\\uD83D\\uDCBC Business Inquiries\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 129,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"For partnerships and business opportunities:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 114,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 111,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 39,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"faq-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"faq-grid\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How secure is my family's health data?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Your data is stored locally on your device using SQLite database with offline-first architecture. We don't store your personal health information on our servers.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I use FamilyMedManager on multiple devices?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! FamilyMedManager works on iOS, Android, and Web platforms. Data synchronization features are available to keep your information consistent across devices.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Is there a cost to use FamilyMedManager?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"FamilyMedManager offers both free and premium tiers. The free version includes core medication tracking features, while premium unlocks AI-powered recommendations and advanced analytics.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How does the AI search feature work?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Our AI feature analyzes your symptoms and available medication inventory to provide personalized recommendations and first aid guidance, powered by OpenAI's advanced language models.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 167,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"support-hours\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Support Hours\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hours-info\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Email Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Monday - Friday, 9:00 AM - 6:00 PM EST\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Emergency Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Available 24/7 for critical issues\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 180,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Response Time:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 181,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" We typically respond within 24 hours during business days\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n            lineNumber: 30,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n        lineNumber: 29,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/contact.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fcontact&preferredRegion=&absolutePagePath=.%2Fpages%2Fcontact.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();