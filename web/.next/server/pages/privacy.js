/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/privacy";
exports.ids = ["pages/privacy"];
exports.modules = {

/***/ "./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprivacy&preferredRegion=&absolutePagePath=.%2Fpages%2Fprivacy.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprivacy&preferredRegion=&absolutePagePath=.%2Fpages%2Fprivacy.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/pages/module.compiled */ \"./node_modules/next/dist/server/future/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"./node_modules/next/dist/pages/_document.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"./pages/_app.tsx\");\n/* harmony import */ var _pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/privacy.tsx */ \"./pages/privacy.tsx\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"default\"));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticProps\");\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getStaticPaths\");\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"getServerSideProps\");\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"config\");\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"reportWebVitals\");\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticProps\");\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticPaths\");\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getStaticParams\");\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerProps\");\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__, \"unstable_getServerSideProps\");\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/privacy\",\n        pathname: \"/privacy\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\"\n    },\n    components: {\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: (private_next_pages_document__WEBPACK_IMPORTED_MODULE_3___default())\n    },\n    userland: _pages_privacy_tsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprivacy&preferredRegion=&absolutePagePath=.%2Fpages%2Fprivacy.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "./components/Layout.tsx":
/*!*******************************!*\
  !*** ./components/Layout.tsx ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Layout: () => (/* binding */ Layout),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Layout = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"site-header\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"site-logo\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"FamilyMedManager \\uD83D\\uDC8A\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                lineNumber: 10,\n                                columnNumber: 25\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 21\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                            className: \"nav\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/about\",\n                                    children: \"About\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/contact\",\n                                    children: \"Contact\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 17,\n                                    columnNumber: 25\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/privacy\",\n                                    children: \"Privacy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 25\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 21\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                lineNumber: 7,\n                columnNumber: 13\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 17\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/components/Layout.tsx\",\n        lineNumber: 6,\n        columnNumber: 9\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./components/Layout.tsx\n");

/***/ }),

/***/ "./pages/_app.tsx":
/*!************************!*\
  !*** ./pages/_app.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../styles/globals.css */ \"./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/_app.tsx\",\n        lineNumber: 5,\n        columnNumber: 12\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9fYXBwLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBOEI7QUFHZixTQUFTQSxJQUFJLEVBQUVDLFNBQVMsRUFBRUMsU0FBUyxFQUFZO0lBQzFELHFCQUFPLDhEQUFDRDtRQUFXLEdBQUdDLFNBQVM7Ozs7OztBQUNuQyIsInNvdXJjZXMiOlsid2VicGFjazovL2ZhbWlseS1tZWQtbWFuYWdlci13ZWIvLi9wYWdlcy9fYXBwLnRzeD8yZmJlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAnLi4vc3R5bGVzL2dsb2JhbHMuY3NzJ1xuaW1wb3J0IHR5cGUgeyBBcHBQcm9wcyB9IGZyb20gJ25leHQvYXBwJ1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBBcHAoeyBDb21wb25lbnQsIHBhZ2VQcm9wcyB9OiBBcHBQcm9wcykge1xuICAgIHJldHVybiA8Q29tcG9uZW50IHsuLi5wYWdlUHJvcHN9IC8+XG59XG4iXSwibmFtZXMiOlsiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///./pages/_app.tsx\n");

/***/ }),

/***/ "./pages/privacy.tsx":
/*!***************************!*\
  !*** ./pages/privacy.tsx ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Privacy)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.tsx\");\n\n\nfunction Privacy() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"privacy-page\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"page-hero\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"Privacy Policy\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hero-subtitle\",\n                            children: \"Your privacy and the security of your family's health information is our top priority\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"last-updated\",\n                            children: \"Last updated: October 4, 2025\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 13,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"content-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Introduction\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"At FamilyMedManager, we understand that your family's health information is deeply personal and sensitive. This Privacy Policy explains how we collect, use, protect, and handle your information when you use our medication management application and related services.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We are committed to maintaining the highest standards of privacy and security, employing a privacy-by-design approach that prioritizes local data storage and minimal data collection.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"content-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Information We Collect\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 32,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Personal Health Information\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 34,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"When you use FamilyMedManager, you may provide:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 35,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Family member names and basic demographic information (age categories: adult/child)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 37,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Medication names, dosages, and administration schedules\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 38,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Medication inventory levels and expiration dates\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 39,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Symptom information when using our AI search feature\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 40,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Notes and additional context related to medication management\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 41,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Technical Information\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 44,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We may automatically collect:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Device information (operating system, app version)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Usage analytics (feature usage, error logs)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Performance data to improve app functionality\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 49,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"AI Search Interactions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"When you use our AI-powered search feature:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 53,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Symptom descriptions and severity assessments\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 55,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Patient type information (adult/child)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Additional context you provide\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 57,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Search interaction logs (stored locally for analytics)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 58,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"content-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"How We Use Your Information\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We use your information solely to provide and improve our services:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 65,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Core Functionality\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Medication tracking and schedule management\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 69,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Inventory monitoring and refill reminders\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Family member profile management\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 71,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Data synchronization across your devices\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 68,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"AI-Powered Features\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 75,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Providing personalized medication recommendations\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 77,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Offering first aid guidance based on symptoms\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Analyzing your medication inventory for relevant suggestions\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 79,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Service Improvement\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Analyzing usage patterns to enhance user experience\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 84,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Identifying and fixing technical issues\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Developing new features based on user needs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"content-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Data Storage and Security\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Local-First Architecture\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"FamilyMedManager employs a local-first data storage approach. Your personal health information is primarily stored on your device using a secure SQLite database. This means:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 95,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Your data remains on your device and under your control\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"The app functions offline without requiring internet connectivity\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"We cannot access your personal health data stored locally\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Security Measures\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We implement multiple layers of security:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 106,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Encrypted local database storage\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 108,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Secure API communications using HTTPS\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Regular security audits and updates\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Access controls and authentication mechanisms\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 107,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Cloud Services\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 114,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"When using AI search features, symptom and medication data may be temporarily processed by OpenAI's services to generate recommendations. This data is:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Transmitted securely using encryption\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Not stored permanently by third-party services\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 121,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Anonymized and contains no personally identifiable information\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"content-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Data Sharing and Disclosure\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We do not sell, rent, or share your personal health information with third parties, except:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Service Providers\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"OpenAI for AI-powered search functionality (anonymized data only)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 133,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Cloud infrastructure providers for app delivery and updates\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Analytics services for app performance monitoring (aggregated data only)\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Legal Requirements\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We may disclose information if required by law or to:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Comply with legal processes or government requests\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Protect our rights, property, or safety\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Prevent fraud or security threats\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"content-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Your Privacy Rights\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"You have the following rights regarding your data:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Access and Control\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 152,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"View all data stored in the app through the interface\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Export your data in a portable format\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Delete specific medications, family members, or all data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Control which features and permissions the app uses\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Data Portability\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Export your medication and family data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Transfer data between devices\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 163,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Create backups of your information\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 161,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            children: \"Deletion Rights\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Delete your account and all associated data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 169,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Remove specific information or family members\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Clear search history and interaction logs\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 148,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"content-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Children's Privacy\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"FamilyMedManager is designed for family use and may include information about children's medications. We take special care to protect children's privacy:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 178,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"We do not knowingly collect personal information directly from children under 13\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 183,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"All children's information is managed by parent or guardian accounts\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Children's data receives the same security protections as adult data\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Parents can review and delete their children's information at any time\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 182,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 176,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"content-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Updates to This Privacy Policy\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"We may update this Privacy Policy from time to time to reflect changes in our practices or legal requirements. When we make significant changes, we will:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 193,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Notify you through the app or via email\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: 'Update the \"Last updated\" date at the top of this policy'\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Provide a summary of key changes\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Give you the opportunity to review and accept the updated policy\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 201,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 191,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"content-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Contact Us\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 207,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"If you have questions about this Privacy Policy or our privacy practices, please contact us:\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 208,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"contact-info\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Email:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 52\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Subject Line:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Privacy Policy Inquiry\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 213,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Response Time:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" We will respond to privacy inquiries within 48 hours\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 206,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"effective-date\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"This Privacy Policy is effective as of October 4, 2025, and applies to all users of FamilyMedManager.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 25\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 21\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n            lineNumber: 6,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/privacy.tsx\",\n        lineNumber: 5,\n        columnNumber: 9\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/privacy.tsx\n");

/***/ }),

/***/ "./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fprivacy&preferredRegion=&absolutePagePath=.%2Fpages%2Fprivacy.tsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();