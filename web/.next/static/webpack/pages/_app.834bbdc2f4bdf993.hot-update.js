"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \":root {\\n    --bg: #ffffff;\\n    --bg-secondary: #f8fafc;\\n    --bg-accent: #f0f9ff;\\n    --primary: #0ea5a4;\\n    --primary-dark: #0d9488;\\n    --primary-light: #5eead4;\\n    --secondary: #6366f1;\\n    --muted: #6b7280;\\n    --text-primary: #0f172a;\\n    --text-secondary: #475569;\\n    --text-muted: #94a3b8;\\n    --border: #e2e8f0;\\n    --border-light: #f1f5f9;\\n    --success: #10b981;\\n    --warning: #f59e0b;\\n    --error: #ef4444;\\n    --max-w: 1200px;\\n    --max-w-content: 900px;\\n    --border-radius: 12px;\\n    --border-radius-sm: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n}\\n\\n* {\\n    box-sizing: border-box;\\n}\\n\\nhtml,\\nbody,\\n#root {\\n    padding: 0;\\n    margin: 0;\\n    font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;\\n    background: var(--bg);\\n    color: var(--text-primary);\\n    line-height: 1.6;\\n}\\n\\n/* Layout */\\n.container {\\n    max-width: var(--max-w-content);\\n    margin: 0 auto;\\n    padding: 0 24px;\\n}\\n\\n.site-header {\\n    background: var(--bg);\\n    border-bottom: 1px solid var(--border);\\n    position: -webkit-sticky;\\n    position: sticky;\\n    top: 0;\\n    z-index: 100;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n}\\n\\n.site-header .container {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 20px 24px;\\n    max-width: var(--max-w);\\n}\\n\\n.nav {\\n    display: flex;\\n    gap: 32px;\\n}\\n\\n.nav a {\\n    color: var(--text-secondary);\\n    text-decoration: none;\\n    font-weight: 500;\\n    transition: color 0.2s ease;\\n    position: relative;\\n}\\n\\n.nav a:hover {\\n    color: var(--primary);\\n}\\n\\n.nav a::after {\\n    content: '';\\n    position: absolute;\\n    bottom: -4px;\\n    left: 0;\\n    width: 0;\\n    height: 2px;\\n    background: var(--primary);\\n    transition: width 0.2s ease;\\n}\\n\\n.nav a:hover::after {\\n    width: 100%;\\n}\\n\\n/* Typography */\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n    margin: 0 0 16px 0;\\n    font-weight: 700;\\n    line-height: 1.2;\\n    color: var(--text-primary);\\n}\\n\\nh1 {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n}\\n\\nh2 {\\n    font-size: 2.25rem;\\n    margin-bottom: 20px;\\n}\\n\\nh3 {\\n    font-size: 1.875rem;\\n    margin-bottom: 16px;\\n}\\n\\nh4 {\\n    font-size: 1.5rem;\\n    margin-bottom: 12px;\\n}\\n\\np {\\n    margin: 0 0 16px 0;\\n    color: var(--text-secondary);\\n}\\n\\nul,\\nol {\\n    margin: 0 0 16px 0;\\n    padding-left: 24px;\\n}\\n\\nli {\\n    margin-bottom: 8px;\\n    color: var(--text-secondary);\\n}\\n\\na {\\n    color: var(--primary);\\n    text-decoration: none;\\n    transition: color 0.2s ease;\\n}\\n\\na:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n/* Buttons */\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline {\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n    padding: 12px 24px;\\n    border-radius: var(--border-radius-sm);\\n    font-weight: 600;\\n    font-size: 16px;\\n    text-decoration: none;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n    gap: 8px;\\n}\\n\\n.btn-primary {\\n    background: var(--primary);\\n    color: white;\\n    box-shadow: var(--shadow);\\n}\\n\\n.btn-primary:hover {\\n    background: var(--primary-dark);\\n    transform: translateY(-1px);\\n    box-shadow: var(--shadow-lg);\\n    text-decoration: none;\\n    color: white;\\n}\\n\\n.btn-secondary {\\n    background: var(--bg-secondary);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border);\\n}\\n\\n.btn-secondary:hover {\\n    background: var(--border-light);\\n    transform: translateY(-1px);\\n    text-decoration: none;\\n    color: var(--text-primary);\\n}\\n\\n.btn-outline {\\n    background: transparent;\\n    color: var(--primary);\\n    border: 2px solid var(--primary);\\n}\\n\\n.btn-outline:hover {\\n    background: var(--primary);\\n    color: white;\\n    text-decoration: none;\\n}\\n\\n.btn-primary.large,\\n.btn-outline.large {\\n    padding: 16px 32px;\\n    font-size: 18px;\\n}\\n\\n/* Cards */\\n.card {\\n    background: var(--bg);\\n    border: 1px solid var(--border);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    box-shadow: var(--shadow);\\n    transition: all 0.2s ease;\\n}\\n\\n.card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n/* Hero Sections */\\n.hero {\\n    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg) 100%);\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.hero-content {\\n    max-width: 800px;\\n    margin: 0 auto;\\n}\\n\\n.hero-title {\\n    font-size: 3.5rem;\\n    margin-bottom: 24px;\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n}\\n\\n.hero-subtitle {\\n    font-size: 1.25rem;\\n    color: var(--text-secondary);\\n    margin-bottom: 40px;\\n    line-height: 1.6;\\n}\\n\\n.hero-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n.page-hero {\\n    background: var(--bg-secondary);\\n    padding: 60px 0;\\n    text-align: center;\\n    border-bottom: 1px solid var(--border);\\n}\\n\\n.last-updated {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n    margin-top: 16px;\\n}\\n\\n/* Sections */\\n.content-section {\\n    padding: 60px 0;\\n    border-bottom: 1px solid var(--border-light);\\n}\\n\\n.content-section:last-child {\\n    border-bottom: none;\\n}\\n\\n.section-header {\\n    text-align: center;\\n    margin-bottom: 60px;\\n}\\n\\n.section-subtitle {\\n    font-size: 1.125rem;\\n    color: var(--text-secondary);\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n/* Features Grid */\\n.features-section {\\n    padding: 80px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.features-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 60px;\\n}\\n\\n.feature-card {\\n    background: var(--bg);\\n    padding: 40px 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    box-shadow: var(--shadow);\\n    transition: all 0.3s ease;\\n    border: 1px solid var(--border);\\n}\\n\\n.feature-card:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-xl);\\n}\\n\\n.feature-icon {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n    display: block;\\n}\\n\\n.feature-card h3 {\\n    color: var(--text-primary);\\n    margin-bottom: 16px;\\n}\\n\\n.feature-card p {\\n    color: var(--text-secondary);\\n    line-height: 1.6;\\n}\\n\\n/* Benefits Grid */\\n.benefits-section {\\n    padding: 80px 0;\\n}\\n\\n.benefits-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.benefit-item {\\n    text-align: center;\\n    padding: 24px;\\n}\\n\\n.benefit-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n    font-size: 1.25rem;\\n}\\n\\n/* CTA Section */\\n.cta-section {\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\\n    color: white;\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.cta-content {\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n.cta-section h2 {\\n    color: white;\\n    margin-bottom: 24px;\\n}\\n\\n.cta-section p {\\n    color: rgba(255, 255, 255, 0.9);\\n    font-size: 1.125rem;\\n    margin-bottom: 40px;\\n}\\n\\n.cta-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n/* Values Grid */\\n.values-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.value-item {\\n    padding: 32px 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n}\\n\\n.value-item h3 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Tech List */\\n.tech-list {\\n    list-style: none;\\n    padding: 0;\\n    margin-top: 32px;\\n}\\n\\n.tech-list li {\\n    padding: 16px 0;\\n    border-bottom: 1px solid var(--border-light);\\n    display: flex;\\n    align-items: flex-start;\\n    gap: 12px;\\n}\\n\\n.tech-list li:last-child {\\n    border-bottom: none;\\n}\\n\\n.tech-list li::before {\\n    content: '✓';\\n    color: var(--success);\\n    font-weight: bold;\\n    flex-shrink: 0;\\n    margin-top: 2px;\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;IACI,aAAa;IACb,uBAAuB;IACvB,oBAAoB;IACpB,kBAAkB;IAClB,uBAAuB;IACvB,wBAAwB;IACxB,oBAAoB;IACpB,gBAAgB;IAChB,uBAAuB;IACvB,yBAAyB;IACzB,qBAAqB;IACrB,iBAAiB;IACjB,uBAAuB;IACvB,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,sBAAsB;IACtB,qBAAqB;IACrB,uBAAuB;IACvB,uEAAuE;IACvE,+EAA+E;IAC/E,gFAAgF;AACpF;;AAEA;IACI,sBAAsB;AAC1B;;AAEA;;;IAGI,UAAU;IACV,SAAS;IACT,yFAAyF;IACzF,qBAAqB;IACrB,0BAA0B;IAC1B,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,+BAA+B;IAC/B,cAAc;IACd,eAAe;AACnB;;AAEA;IACI,qBAAqB;IACrB,sCAAsC;IACtC,wBAAgB;IAAhB,gBAAgB;IAChB,MAAM;IACN,YAAY;IACZ,mCAAmC;IACnC,2BAA2B;AAC/B;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,8BAA8B;IAC9B,kBAAkB;IAClB,uBAAuB;AAC3B;;AAEA;IACI,aAAa;IACb,SAAS;AACb;;AAEA;IACI,4BAA4B;IAC5B,qBAAqB;IACrB,gBAAgB;IAChB,2BAA2B;IAC3B,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;AACzB;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,WAAW;IACX,0BAA0B;IAC1B,2BAA2B;AAC/B;;AAEA;IACI,WAAW;AACf;;AAEA,eAAe;AACf;;;;;;IAMI,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,0BAA0B;AAC9B;;AAEA;IACI,eAAe;IACf,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;AAChC;;AAEA;;IAEI,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;AAChC;;AAEA;IACI,qBAAqB;IACrB,qBAAqB;IACrB,2BAA2B;AAC/B;;AAEA;IACI,0BAA0B;IAC1B,0BAA0B;AAC9B;;AAEA,YAAY;AACZ;;;IAGI,oBAAoB;IACpB,mBAAmB;IACnB,uBAAuB;IACvB,kBAAkB;IAClB,sCAAsC;IACtC,gBAAgB;IAChB,eAAe;IACf,qBAAqB;IACrB,YAAY;IACZ,eAAe;IACf,yBAAyB;IACzB,QAAQ;AACZ;;AAEA;IACI,0BAA0B;IAC1B,YAAY;IACZ,yBAAyB;AAC7B;;AAEA;IACI,+BAA+B;IAC/B,2BAA2B;IAC3B,4BAA4B;IAC5B,qBAAqB;IACrB,YAAY;AAChB;;AAEA;IACI,+BAA+B;IAC/B,0BAA0B;IAC1B,+BAA+B;AACnC;;AAEA;IACI,+BAA+B;IAC/B,2BAA2B;IAC3B,qBAAqB;IACrB,0BAA0B;AAC9B;;AAEA;IACI,uBAAuB;IACvB,qBAAqB;IACrB,gCAAgC;AACpC;;AAEA;IACI,0BAA0B;IAC1B,YAAY;IACZ,qBAAqB;AACzB;;AAEA;;IAEI,kBAAkB;IAClB,eAAe;AACnB;;AAEA,UAAU;AACV;IACI,qBAAqB;IACrB,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,yBAAyB;IACzB,yBAAyB;AAC7B;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA,kBAAkB;AAClB;IACI,wEAAwE;IACxE,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,cAAc;AAClB;;AAEA;IACI,iBAAiB;IACjB,mBAAmB;IACnB,6EAA6E;IAC7E,6BAA6B;IAC7B,oCAAoC;IACpC,qBAAqB;AACzB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;IAC5B,mBAAmB;IACnB,gBAAgB;AACpB;;AAEA;IACI,aAAa;IACb,SAAS;IACT,uBAAuB;IACvB,eAAe;AACnB;;AAEA;IACI,+BAA+B;IAC/B,eAAe;IACf,kBAAkB;IAClB,sCAAsC;AAC1C;;AAEA;IACI,wBAAwB;IACxB,eAAe;IACf,gBAAgB;AACpB;;AAEA,aAAa;AACb;IACI,eAAe;IACf,4CAA4C;AAChD;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,4BAA4B;IAC5B,gBAAgB;IAChB,cAAc;AAClB;;AAEA,kBAAkB;AAClB;IACI,eAAe;IACf,+BAA+B;AACnC;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,kBAAkB;IAClB,mCAAmC;IACnC,kBAAkB;IAClB,yBAAyB;IACzB,yBAAyB;IACzB,+BAA+B;AACnC;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA;IACI,eAAe;IACf,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,0BAA0B;IAC1B,mBAAmB;AACvB;;AAEA;IACI,4BAA4B;IAC5B,gBAAgB;AACpB;;AAEA,kBAAkB;AAClB;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,aAAa;AACjB;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA,gBAAgB;AAChB;IACI,gFAAgF;IAChF,YAAY;IACZ,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,cAAc;AAClB;;AAEA;IACI,YAAY;IACZ,mBAAmB;AACvB;;AAEA;IACI,+BAA+B;IAC/B,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,aAAa;IACb,SAAS;IACT,uBAAuB;IACvB,eAAe;AACnB;;AAEA,gBAAgB;AAChB;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,+BAA+B;IAC/B,mCAAmC;IACnC,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;AACvB;;AAEA,cAAc;AACd;IACI,gBAAgB;IAChB,UAAU;IACV,gBAAgB;AACpB;;AAEA;IACI,eAAe;IACf,4CAA4C;IAC5C,aAAa;IACb,uBAAuB;IACvB,SAAS;AACb;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,YAAY;IACZ,qBAAqB;IACrB,iBAAiB;IACjB,cAAc;IACd,eAAe;AACnB\",\"sourcesContent\":[\":root {\\n    --bg: #ffffff;\\n    --bg-secondary: #f8fafc;\\n    --bg-accent: #f0f9ff;\\n    --primary: #0ea5a4;\\n    --primary-dark: #0d9488;\\n    --primary-light: #5eead4;\\n    --secondary: #6366f1;\\n    --muted: #6b7280;\\n    --text-primary: #0f172a;\\n    --text-secondary: #475569;\\n    --text-muted: #94a3b8;\\n    --border: #e2e8f0;\\n    --border-light: #f1f5f9;\\n    --success: #10b981;\\n    --warning: #f59e0b;\\n    --error: #ef4444;\\n    --max-w: 1200px;\\n    --max-w-content: 900px;\\n    --border-radius: 12px;\\n    --border-radius-sm: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n}\\n\\n* {\\n    box-sizing: border-box;\\n}\\n\\nhtml,\\nbody,\\n#root {\\n    padding: 0;\\n    margin: 0;\\n    font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;\\n    background: var(--bg);\\n    color: var(--text-primary);\\n    line-height: 1.6;\\n}\\n\\n/* Layout */\\n.container {\\n    max-width: var(--max-w-content);\\n    margin: 0 auto;\\n    padding: 0 24px;\\n}\\n\\n.site-header {\\n    background: var(--bg);\\n    border-bottom: 1px solid var(--border);\\n    position: sticky;\\n    top: 0;\\n    z-index: 100;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n}\\n\\n.site-header .container {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 20px 24px;\\n    max-width: var(--max-w);\\n}\\n\\n.nav {\\n    display: flex;\\n    gap: 32px;\\n}\\n\\n.nav a {\\n    color: var(--text-secondary);\\n    text-decoration: none;\\n    font-weight: 500;\\n    transition: color 0.2s ease;\\n    position: relative;\\n}\\n\\n.nav a:hover {\\n    color: var(--primary);\\n}\\n\\n.nav a::after {\\n    content: '';\\n    position: absolute;\\n    bottom: -4px;\\n    left: 0;\\n    width: 0;\\n    height: 2px;\\n    background: var(--primary);\\n    transition: width 0.2s ease;\\n}\\n\\n.nav a:hover::after {\\n    width: 100%;\\n}\\n\\n/* Typography */\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n    margin: 0 0 16px 0;\\n    font-weight: 700;\\n    line-height: 1.2;\\n    color: var(--text-primary);\\n}\\n\\nh1 {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n}\\n\\nh2 {\\n    font-size: 2.25rem;\\n    margin-bottom: 20px;\\n}\\n\\nh3 {\\n    font-size: 1.875rem;\\n    margin-bottom: 16px;\\n}\\n\\nh4 {\\n    font-size: 1.5rem;\\n    margin-bottom: 12px;\\n}\\n\\np {\\n    margin: 0 0 16px 0;\\n    color: var(--text-secondary);\\n}\\n\\nul,\\nol {\\n    margin: 0 0 16px 0;\\n    padding-left: 24px;\\n}\\n\\nli {\\n    margin-bottom: 8px;\\n    color: var(--text-secondary);\\n}\\n\\na {\\n    color: var(--primary);\\n    text-decoration: none;\\n    transition: color 0.2s ease;\\n}\\n\\na:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n/* Buttons */\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline {\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n    padding: 12px 24px;\\n    border-radius: var(--border-radius-sm);\\n    font-weight: 600;\\n    font-size: 16px;\\n    text-decoration: none;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n    gap: 8px;\\n}\\n\\n.btn-primary {\\n    background: var(--primary);\\n    color: white;\\n    box-shadow: var(--shadow);\\n}\\n\\n.btn-primary:hover {\\n    background: var(--primary-dark);\\n    transform: translateY(-1px);\\n    box-shadow: var(--shadow-lg);\\n    text-decoration: none;\\n    color: white;\\n}\\n\\n.btn-secondary {\\n    background: var(--bg-secondary);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border);\\n}\\n\\n.btn-secondary:hover {\\n    background: var(--border-light);\\n    transform: translateY(-1px);\\n    text-decoration: none;\\n    color: var(--text-primary);\\n}\\n\\n.btn-outline {\\n    background: transparent;\\n    color: var(--primary);\\n    border: 2px solid var(--primary);\\n}\\n\\n.btn-outline:hover {\\n    background: var(--primary);\\n    color: white;\\n    text-decoration: none;\\n}\\n\\n.btn-primary.large,\\n.btn-outline.large {\\n    padding: 16px 32px;\\n    font-size: 18px;\\n}\\n\\n/* Cards */\\n.card {\\n    background: var(--bg);\\n    border: 1px solid var(--border);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    box-shadow: var(--shadow);\\n    transition: all 0.2s ease;\\n}\\n\\n.card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n/* Hero Sections */\\n.hero {\\n    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg) 100%);\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.hero-content {\\n    max-width: 800px;\\n    margin: 0 auto;\\n}\\n\\n.hero-title {\\n    font-size: 3.5rem;\\n    margin-bottom: 24px;\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n}\\n\\n.hero-subtitle {\\n    font-size: 1.25rem;\\n    color: var(--text-secondary);\\n    margin-bottom: 40px;\\n    line-height: 1.6;\\n}\\n\\n.hero-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n.page-hero {\\n    background: var(--bg-secondary);\\n    padding: 60px 0;\\n    text-align: center;\\n    border-bottom: 1px solid var(--border);\\n}\\n\\n.last-updated {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n    margin-top: 16px;\\n}\\n\\n/* Sections */\\n.content-section {\\n    padding: 60px 0;\\n    border-bottom: 1px solid var(--border-light);\\n}\\n\\n.content-section:last-child {\\n    border-bottom: none;\\n}\\n\\n.section-header {\\n    text-align: center;\\n    margin-bottom: 60px;\\n}\\n\\n.section-subtitle {\\n    font-size: 1.125rem;\\n    color: var(--text-secondary);\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n/* Features Grid */\\n.features-section {\\n    padding: 80px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.features-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    gap: 32px;\\n    margin-top: 60px;\\n}\\n\\n.feature-card {\\n    background: var(--bg);\\n    padding: 40px 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    box-shadow: var(--shadow);\\n    transition: all 0.3s ease;\\n    border: 1px solid var(--border);\\n}\\n\\n.feature-card:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-xl);\\n}\\n\\n.feature-icon {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n    display: block;\\n}\\n\\n.feature-card h3 {\\n    color: var(--text-primary);\\n    margin-bottom: 16px;\\n}\\n\\n.feature-card p {\\n    color: var(--text-secondary);\\n    line-height: 1.6;\\n}\\n\\n/* Benefits Grid */\\n.benefits-section {\\n    padding: 80px 0;\\n}\\n\\n.benefits-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.benefit-item {\\n    text-align: center;\\n    padding: 24px;\\n}\\n\\n.benefit-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n    font-size: 1.25rem;\\n}\\n\\n/* CTA Section */\\n.cta-section {\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\\n    color: white;\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.cta-content {\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n.cta-section h2 {\\n    color: white;\\n    margin-bottom: 24px;\\n}\\n\\n.cta-section p {\\n    color: rgba(255, 255, 255, 0.9);\\n    font-size: 1.125rem;\\n    margin-bottom: 40px;\\n}\\n\\n.cta-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n/* Values Grid */\\n.values-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.value-item {\\n    padding: 32px 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n}\\n\\n.value-item h3 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Tech List */\\n.tech-list {\\n    list-style: none;\\n    padding: 0;\\n    margin-top: 32px;\\n}\\n\\n.tech-list li {\\n    padding: 16px 0;\\n    border-bottom: 1px solid var(--border-light);\\n    display: flex;\\n    align-items: flex-start;\\n    gap: 12px;\\n}\\n\\n.tech-list li:last-child {\\n    border-bottom: none;\\n}\\n\\n.tech-list li::before {\\n    content: '✓';\\n    color: var(--success);\\n    font-weight: bold;\\n    flex-shrink: 0;\\n    margin-top: 2px;\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});