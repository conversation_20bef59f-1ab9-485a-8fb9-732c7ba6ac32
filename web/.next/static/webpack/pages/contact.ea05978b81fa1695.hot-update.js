"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/contact",{

/***/ "./pages/contact.tsx":
/*!***************************!*\
  !*** ./pages/contact.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Contact; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.tsx\");\n\n\nfunction Contact() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"contact-page\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"page-hero\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"Contact & Support\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hero-subtitle\",\n                            children: \"We're here to help you get the most out of FamilyMedManager\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"contact-info-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Get in Touch\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Have a question, suggestion, or need support? We'd love to hear from you.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"contact-methods\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-method\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"\\uD83D\\uDCE7 General Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"For all inquiries, support, and feedback:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-method\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"\\uD83D\\uDEA8 Technical Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"For urgent technical issues and bug reports:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                            children: 'Please include \"URGENT\" in the subject line for priority support'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-method\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"\\uD83D\\uDCA1 Feature Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Have an idea to improve FamilyMedManager?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                            children: \"We love hearing from our users about new features!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"faq-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"faq-grid\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How secure is my family's health data?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Your data is stored locally on your device using SQLite database with offline-first architecture. We don't store your personal health information on our servers.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I use FamilyMedManager on multiple devices?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! FamilyMedManager works on iOS, Android, and Web platforms. Data synchronization features are available to keep your information consistent across devices.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Is there a cost to use FamilyMedManager?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"FamilyMedManager offers both free and premium tiers. The free version includes core medication tracking features, while premium unlocks AI-powered recommendations and advanced analytics.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How does the AI search feature work?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Our AI feature analyzes your symptoms and available medication inventory to provide personalized recommendations and first aid guidance, powered by OpenAI's advanced language models.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"support-hours\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Support Hours\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hours-info\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Email Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 86,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Monday - Friday, 9:00 AM - 6:00 PM EST\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Emergency Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 87,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Available 24/7 for critical issues\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Response Time:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" We typically respond within 24 hours during business days\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 85,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n            lineNumber: 7,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n        lineNumber: 6,\n        columnNumber: 9\n    }, this);\n}\n_c = Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/contact.tsx\n"));

/***/ })

});