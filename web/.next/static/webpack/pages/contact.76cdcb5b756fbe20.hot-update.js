"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/contact",{

/***/ "./pages/contact.tsx":
/*!***************************!*\
  !*** ./pages/contact.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Contact; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.tsx\");\n\n\nfunction Contact() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"contact-page\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"page-hero\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"Contact & Support\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hero-subtitle\",\n                            children: \"We're here to help you get the most out of FamilyMedManager\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"contact-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"contact-form-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Get in Touch\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 19,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Have a question, suggestion, or need support? We'd love to hear from you.\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 20,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    className: \"contact-form\",\n                                    onSubmit: handleSubmit,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"name\",\n                                                    children: \"Full Name *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 24,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"text\",\n                                                    id: \"name\",\n                                                    name: \"name\",\n                                                    value: formData.name,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 25,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"email\",\n                                                    children: \"Email Address *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 37,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                    type: \"email\",\n                                                    id: \"email\",\n                                                    name: \"email\",\n                                                    value: formData.email,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"form-input\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 38,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"subject\",\n                                                    children: \"Subject *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 50,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                    id: \"subject\",\n                                                    name: \"subject\",\n                                                    value: formData.subject,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    className: \"form-input\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"\",\n                                                            children: \"Select a subject\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 59,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"general\",\n                                                            children: \"General Inquiry\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 60,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"support\",\n                                                            children: \"Technical Support\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 61,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"feature\",\n                                                            children: \"Feature Request\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 62,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"bug\",\n                                                            children: \"Bug Report\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 63,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"partnership\",\n                                                            children: \"Partnership\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 64,\n                                                            columnNumber: 37\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                            value: \"other\",\n                                                            children: \"Other\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                            lineNumber: 65,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 51,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"form-group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                    htmlFor: \"message\",\n                                                    children: \"Message *\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 70,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                                    id: \"message\",\n                                                    name: \"message\",\n                                                    value: formData.message,\n                                                    onChange: handleInputChange,\n                                                    required: true,\n                                                    rows: 6,\n                                                    className: \"form-input\",\n                                                    placeholder: \"Please describe your question or issue in detail...\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 71,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 69,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            type: \"submit\",\n                                            className: \"btn-primary\",\n                                            children: \"Send Message\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 83,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                            className: \"contact-info-section\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    children: \"Other Ways to Reach Us\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-methods\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"contact-method\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: \"\\uD83D\\uDCE7 Email Support\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"For general inquiries and support:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 95,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 92,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"contact-method\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: \"\\uD83D\\uDEA8 Emergency Support\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 99,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"For urgent technical issues:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 100,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 101,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                                    children: \"Response within 4 hours during business days\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 102,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"contact-method\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    children: \"\\uD83D\\uDCBC Business Inquiries\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 106,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"For partnerships and business opportunities:\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 107,\n                                                    columnNumber: 33\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                    href: \"mailto:<EMAIL>\",\n                                                    children: \"<EMAIL>\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 33\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 91,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"faq-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"faq-grid\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How secure is my family's health data?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 119,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Your data is stored locally on your device using SQLite database with offline-first architecture. We don't store your personal health information on our servers.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 120,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I use FamilyMedManager on multiple devices?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! FamilyMedManager works on iOS, Android, and Web platforms. Data synchronization features are available to keep your information consistent across devices.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 126,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Is there a cost to use FamilyMedManager?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 135,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"FamilyMedManager offers both free and premium tiers. The free version includes core medication tracking features, while premium unlocks AI-powered recommendations and advanced analytics.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 136,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How does the AI search feature work?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 143,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Our AI feature analyzes your symptoms and available medication inventory to provide personalized recommendations and first aid guidance, powered by OpenAI's advanced language models.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 144,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 117,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"support-hours\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Support Hours\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 154,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hours-info\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Email Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Monday - Friday, 9:00 AM - 6:00 PM EST\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 156,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Emergency Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Available 24/7 for critical issues\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 157,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Response Time:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 158,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" We typically respond within 24 hours during business days\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 158,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 155,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 153,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n            lineNumber: 7,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n        lineNumber: 6,\n        columnNumber: 9\n    }, this);\n}\n_c = Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/contact.tsx\n"));

/***/ })

});