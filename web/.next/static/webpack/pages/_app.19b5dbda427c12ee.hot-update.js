"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \":root {\\n    --bg: #ffffff;\\n    --bg-secondary: #f8fafc;\\n    --bg-accent: #f0f9ff;\\n    --primary: #0ea5a4;\\n    --primary-dark: #0d9488;\\n    --primary-light: #5eead4;\\n    --secondary: #6366f1;\\n    --muted: #6b7280;\\n    --text-primary: #0f172a;\\n    --text-secondary: #475569;\\n    --text-muted: #94a3b8;\\n    --border: #e2e8f0;\\n    --border-light: #f1f5f9;\\n    --success: #10b981;\\n    --warning: #f59e0b;\\n    --error: #ef4444;\\n    --max-w: 1200px;\\n    --max-w-content: 900px;\\n    --border-radius: 12px;\\n    --border-radius-sm: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n}\\n\\n* {\\n    box-sizing: border-box;\\n}\\n\\nhtml,\\nbody,\\n#root {\\n    padding: 0;\\n    margin: 0;\\n    font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;\\n    background: var(--bg);\\n    color: var(--text-primary);\\n    line-height: 1.6;\\n}\\n\\n/* Layout */\\n.container {\\n    max-width: var(--max-w-content);\\n    margin: 0 auto;\\n    padding: 0 24px;\\n}\\n\\n.site-header {\\n    background: var(--bg);\\n    border-bottom: 1px solid var(--border);\\n    position: -webkit-sticky;\\n    position: sticky;\\n    top: 0;\\n    z-index: 100;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n}\\n\\n.site-header .container {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 20px 24px;\\n    max-width: var(--max-w);\\n}\\n\\n.site-logo {\\n    display: flex;\\n    align-items: center;\\n    gap: 12px;\\n}\\n\\n.site-logo strong {\\n    font-size: 1.25rem;\\n    color: var(--primary);\\n}\\n\\n.nav {\\n    display: flex;\\n    gap: 32px;\\n}\\n\\n.nav a {\\n    color: var(--text-secondary);\\n    text-decoration: none;\\n    font-weight: 500;\\n    transition: color 0.2s ease;\\n    position: relative;\\n}\\n\\n.nav a:hover {\\n    color: var(--primary);\\n}\\n\\n.nav a::after {\\n    content: '';\\n    position: absolute;\\n    bottom: -4px;\\n    left: 0;\\n    width: 0;\\n    height: 2px;\\n    background: var(--primary);\\n    transition: width 0.2s ease;\\n}\\n\\n.nav a:hover::after {\\n    width: 100%;\\n}\\n\\n/* Typography */\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n    margin: 0 0 16px 0;\\n    font-weight: 700;\\n    line-height: 1.2;\\n    color: var(--text-primary);\\n}\\n\\nh1 {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n}\\n\\nh2 {\\n    font-size: 2.25rem;\\n    margin-bottom: 20px;\\n}\\n\\nh3 {\\n    font-size: 1.875rem;\\n    margin-bottom: 16px;\\n}\\n\\nh4 {\\n    font-size: 1.5rem;\\n    margin-bottom: 12px;\\n}\\n\\np {\\n    margin: 0 0 16px 0;\\n    color: var(--text-secondary);\\n}\\n\\nul,\\nol {\\n    margin: 0 0 16px 0;\\n    padding-left: 24px;\\n}\\n\\nli {\\n    margin-bottom: 8px;\\n    color: var(--text-secondary);\\n}\\n\\na {\\n    color: var(--primary);\\n    text-decoration: none;\\n    transition: color 0.2s ease;\\n}\\n\\na:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n/* Buttons */\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline {\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n    padding: 12px 24px;\\n    border-radius: var(--border-radius-sm);\\n    font-weight: 600;\\n    font-size: 16px;\\n    text-decoration: none;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n    gap: 8px;\\n}\\n\\n.btn-primary {\\n    background: var(--primary);\\n    color: white;\\n    box-shadow: var(--shadow);\\n}\\n\\n.btn-primary:hover {\\n    background: var(--primary-dark);\\n    transform: translateY(-1px);\\n    box-shadow: var(--shadow-lg);\\n    text-decoration: none;\\n    color: white;\\n}\\n\\n.btn-secondary {\\n    background: var(--bg-secondary);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border);\\n}\\n\\n.btn-secondary:hover {\\n    background: var(--border-light);\\n    transform: translateY(-1px);\\n    text-decoration: none;\\n    color: var(--text-primary);\\n}\\n\\n.btn-outline {\\n    background: transparent;\\n    color: var(--primary);\\n    border: 2px solid var(--primary);\\n}\\n\\n.btn-outline:hover {\\n    background: var(--primary);\\n    color: white;\\n    text-decoration: none;\\n}\\n\\n.btn-primary.large,\\n.btn-outline.large {\\n    padding: 16px 32px;\\n    font-size: 18px;\\n}\\n\\n/* Cards */\\n.card {\\n    background: var(--bg);\\n    border: 1px solid var(--border);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    box-shadow: var(--shadow);\\n    transition: all 0.2s ease;\\n}\\n\\n.card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n/* Hero Sections */\\n.hero {\\n    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg) 100%);\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.hero-content {\\n    max-width: 800px;\\n    margin: 0 auto;\\n}\\n\\n.hero-title {\\n    font-size: 3.5rem;\\n    margin-bottom: 24px;\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n}\\n\\n.hero-subtitle {\\n    font-size: 1.25rem;\\n    color: var(--text-secondary);\\n    margin-bottom: 40px;\\n    line-height: 1.6;\\n}\\n\\n.hero-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n.page-hero {\\n    background: var(--bg-secondary);\\n    padding: 60px 0;\\n    text-align: center;\\n    border-bottom: 1px solid var(--border);\\n}\\n\\n.last-updated {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n    margin-top: 16px;\\n}\\n\\n/* Sections */\\n.content-section {\\n    padding: 60px 0;\\n    border-bottom: 1px solid var(--border-light);\\n}\\n\\n.content-section:last-child {\\n    border-bottom: none;\\n}\\n\\n.section-header {\\n    text-align: center;\\n    margin-bottom: 60px;\\n}\\n\\n.section-subtitle {\\n    font-size: 1.125rem;\\n    color: var(--text-secondary);\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n/* Features Grid */\\n.features-section {\\n    padding: 80px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.features-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 60px;\\n}\\n\\n.feature-card {\\n    background: var(--bg);\\n    padding: 40px 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    box-shadow: var(--shadow);\\n    transition: all 0.3s ease;\\n    border: 1px solid var(--border);\\n}\\n\\n.feature-card:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-xl);\\n}\\n\\n.feature-icon {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n    display: block;\\n}\\n\\n.feature-card h3 {\\n    color: var(--text-primary);\\n    margin-bottom: 16px;\\n}\\n\\n.feature-card p {\\n    color: var(--text-secondary);\\n    line-height: 1.6;\\n}\\n\\n/* Benefits Grid */\\n.benefits-section {\\n    padding: 80px 0;\\n}\\n\\n.benefits-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.benefit-item {\\n    text-align: center;\\n    padding: 24px;\\n}\\n\\n.benefit-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n    font-size: 1.25rem;\\n}\\n\\n/* CTA Section */\\n.cta-section {\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\\n    color: white;\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.cta-content {\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n.cta-section h2 {\\n    color: white;\\n    margin-bottom: 24px;\\n}\\n\\n.cta-section p {\\n    color: rgba(255, 255, 255, 0.9);\\n    font-size: 1.125rem;\\n    margin-bottom: 40px;\\n}\\n\\n.cta-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n/* Values Grid */\\n.values-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.value-item {\\n    padding: 32px 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n}\\n\\n.value-item h3 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Tech List */\\n.tech-list {\\n    list-style: none;\\n    padding: 0;\\n    margin-top: 32px;\\n}\\n\\n.tech-list li {\\n    padding: 16px 0;\\n    border-bottom: 1px solid var(--border-light);\\n    display: flex;\\n    align-items: flex-start;\\n    gap: 12px;\\n}\\n\\n.tech-list li:last-child {\\n    border-bottom: none;\\n}\\n\\n.tech-list li::before {\\n    content: '✓';\\n    color: var(--success);\\n    font-weight: bold;\\n    flex-shrink: 0;\\n    margin-top: 2px;\\n}\\n\\n/* Contact Page Styles */\\n.contact-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-content {\\n    display: grid;\\n    grid-template-columns: 2fr 1fr;\\n    grid-gap: 60px;\\n    gap: 60px;\\n    margin: 60px 0;\\n}\\n\\n.contact-form-section h2 {\\n    margin-bottom: 16px;\\n}\\n\\n.contact-form {\\n    background: var(--bg-secondary);\\n    padding: 40px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.form-group {\\n    margin-bottom: 24px;\\n}\\n\\n.form-group label {\\n    display: block;\\n    margin-bottom: 8px;\\n    font-weight: 600;\\n    color: var(--text-primary);\\n}\\n\\n.form-input {\\n    width: 100%;\\n    padding: 12px 16px;\\n    border: 1px solid var(--border);\\n    border-radius: var(--border-radius-sm);\\n    font-size: 16px;\\n    transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n    background: var(--bg);\\n}\\n\\n.form-input:focus {\\n    outline: none;\\n    border-color: var(--primary);\\n    box-shadow: 0 0 0 3px rgba(14, 165, 164, 0.1);\\n}\\n\\n.form-input::placeholder {\\n    color: var(--text-muted);\\n}\\n\\ntextarea.form-input {\\n    resize: vertical;\\n    min-height: 120px;\\n}\\n\\n.contact-info-section {\\n    padding: 40px 0;\\n}\\n\\n.contact-methods {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 32px;\\n}\\n\\n.contact-method {\\n    padding: 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.contact-method h3 {\\n    margin-bottom: 12px;\\n    color: var(--primary);\\n}\\n\\n.contact-method p {\\n    margin-bottom: 8px;\\n}\\n\\n.contact-method small {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n}\\n\\n/* FAQ Styles */\\n.faq-section {\\n    padding: 60px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.faq-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    grid-gap: 24px;\\n    gap: 24px;\\n    margin-top: 40px;\\n}\\n\\n.faq-item {\\n    background: var(--bg);\\n    padding: 24px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n    transition: all 0.2s ease;\\n}\\n\\n.faq-item:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n.faq-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 12px;\\n    font-size: 1.125rem;\\n}\\n\\n.faq-item p {\\n    line-height: 1.6;\\n    margin-bottom: 0;\\n}\\n\\n/* Support Hours */\\n.support-hours {\\n    padding: 40px 0;\\n    text-align: center;\\n}\\n\\n.hours-info {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    margin-top: 24px;\\n}\\n\\n.hours-info p {\\n    margin-bottom: 12px;\\n}\\n\\n/* Privacy Page Styles */\\n.privacy-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-info {\\n    background: var(--bg-secondary);\\n    padding: 24px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n    margin-top: 24px;\\n}\\n\\n.contact-info p {\\n    margin-bottom: 8px;\\n}\\n\\n.effective-date {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    margin-top: 60px;\\n    border: 1px solid var(--primary-light);\\n}\\n\\n/* About Page Styles */\\n.about-page {\\n    padding: 40px 0;\\n}\\n\\n/* Responsive Design */\\n@media (max-width: 768px) {\\n    .hero-title {\\n        font-size: 2.5rem;\\n    }\\n\\n    .hero-subtitle {\\n        font-size: 1.125rem;\\n    }\\n\\n    .hero-buttons {\\n        flex-direction: column;\\n        align-items: center;\\n    }\\n\\n    .features-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .benefits-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .values-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .contact-content {\\n        grid-template-columns: 1fr;\\n        gap: 40px;\\n    }\\n\\n    .faq-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .nav {\\n        gap: 16px;\\n    }\\n\\n    .container {\\n        padding: 0 16px;\\n    }\\n\\n    .site-header .container {\\n        padding: 16px;\\n    }\\n\\n    h1 {\\n        font-size: 2.25rem;\\n    }\\n\\n    h2 {\\n        font-size: 1.875rem;\\n    }\\n\\n    .content-section {\\n        padding: 40px 0;\\n    }\\n\\n    .features-section,\\n    .benefits-section,\\n    .cta-section {\\n        padding: 60px 0;\\n    }\\n}\\n\\n@media (max-width: 480px) {\\n    .hero-title {\\n        font-size: 2rem;\\n    }\\n\\n    .hero {\\n        padding: 60px 0;\\n    }\\n\\n    .feature-card,\\n    .contact-form {\\n        padding: 24px;\\n    }\\n\\n    .btn-primary.large,\\n    .btn-outline.large {\\n        padding: 14px 24px;\\n        font-size: 16px;\\n    }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;IACI,aAAa;IACb,uBAAuB;IACvB,oBAAoB;IACpB,kBAAkB;IAClB,uBAAuB;IACvB,wBAAwB;IACxB,oBAAoB;IACpB,gBAAgB;IAChB,uBAAuB;IACvB,yBAAyB;IACzB,qBAAqB;IACrB,iBAAiB;IACjB,uBAAuB;IACvB,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,sBAAsB;IACtB,qBAAqB;IACrB,uBAAuB;IACvB,uEAAuE;IACvE,+EAA+E;IAC/E,gFAAgF;AACpF;;AAEA;IACI,sBAAsB;AAC1B;;AAEA;;;IAGI,UAAU;IACV,SAAS;IACT,yFAAyF;IACzF,qBAAqB;IACrB,0BAA0B;IAC1B,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,+BAA+B;IAC/B,cAAc;IACd,eAAe;AACnB;;AAEA;IACI,qBAAqB;IACrB,sCAAsC;IACtC,wBAAgB;IAAhB,gBAAgB;IAChB,MAAM;IACN,YAAY;IACZ,mCAAmC;IACnC,2BAA2B;AAC/B;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,8BAA8B;IAC9B,kBAAkB;IAClB,uBAAuB;AAC3B;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,SAAS;AACb;;AAEA;IACI,kBAAkB;IAClB,qBAAqB;AACzB;;AAEA;IACI,aAAa;IACb,SAAS;AACb;;AAEA;IACI,4BAA4B;IAC5B,qBAAqB;IACrB,gBAAgB;IAChB,2BAA2B;IAC3B,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;AACzB;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,WAAW;IACX,0BAA0B;IAC1B,2BAA2B;AAC/B;;AAEA;IACI,WAAW;AACf;;AAEA,eAAe;AACf;;;;;;IAMI,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,0BAA0B;AAC9B;;AAEA;IACI,eAAe;IACf,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;AAChC;;AAEA;;IAEI,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;AAChC;;AAEA;IACI,qBAAqB;IACrB,qBAAqB;IACrB,2BAA2B;AAC/B;;AAEA;IACI,0BAA0B;IAC1B,0BAA0B;AAC9B;;AAEA,YAAY;AACZ;;;IAGI,oBAAoB;IACpB,mBAAmB;IACnB,uBAAuB;IACvB,kBAAkB;IAClB,sCAAsC;IACtC,gBAAgB;IAChB,eAAe;IACf,qBAAqB;IACrB,YAAY;IACZ,eAAe;IACf,yBAAyB;IACzB,QAAQ;AACZ;;AAEA;IACI,0BAA0B;IAC1B,YAAY;IACZ,yBAAyB;AAC7B;;AAEA;IACI,+BAA+B;IAC/B,2BAA2B;IAC3B,4BAA4B;IAC5B,qBAAqB;IACrB,YAAY;AAChB;;AAEA;IACI,+BAA+B;IAC/B,0BAA0B;IAC1B,+BAA+B;AACnC;;AAEA;IACI,+BAA+B;IAC/B,2BAA2B;IAC3B,qBAAqB;IACrB,0BAA0B;AAC9B;;AAEA;IACI,uBAAuB;IACvB,qBAAqB;IACrB,gCAAgC;AACpC;;AAEA;IACI,0BAA0B;IAC1B,YAAY;IACZ,qBAAqB;AACzB;;AAEA;;IAEI,kBAAkB;IAClB,eAAe;AACnB;;AAEA,UAAU;AACV;IACI,qBAAqB;IACrB,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,yBAAyB;IACzB,yBAAyB;AAC7B;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA,kBAAkB;AAClB;IACI,wEAAwE;IACxE,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,cAAc;AAClB;;AAEA;IACI,iBAAiB;IACjB,mBAAmB;IACnB,6EAA6E;IAC7E,6BAA6B;IAC7B,oCAAoC;IACpC,qBAAqB;AACzB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;IAC5B,mBAAmB;IACnB,gBAAgB;AACpB;;AAEA;IACI,aAAa;IACb,SAAS;IACT,uBAAuB;IACvB,eAAe;AACnB;;AAEA;IACI,+BAA+B;IAC/B,eAAe;IACf,kBAAkB;IAClB,sCAAsC;AAC1C;;AAEA;IACI,wBAAwB;IACxB,eAAe;IACf,gBAAgB;AACpB;;AAEA,aAAa;AACb;IACI,eAAe;IACf,4CAA4C;AAChD;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,4BAA4B;IAC5B,gBAAgB;IAChB,cAAc;AAClB;;AAEA,kBAAkB;AAClB;IACI,eAAe;IACf,+BAA+B;AACnC;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,kBAAkB;IAClB,mCAAmC;IACnC,kBAAkB;IAClB,yBAAyB;IACzB,yBAAyB;IACzB,+BAA+B;AACnC;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA;IACI,eAAe;IACf,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,0BAA0B;IAC1B,mBAAmB;AACvB;;AAEA;IACI,4BAA4B;IAC5B,gBAAgB;AACpB;;AAEA,kBAAkB;AAClB;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,aAAa;AACjB;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA,gBAAgB;AAChB;IACI,gFAAgF;IAChF,YAAY;IACZ,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,cAAc;AAClB;;AAEA;IACI,YAAY;IACZ,mBAAmB;AACvB;;AAEA;IACI,+BAA+B;IAC/B,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,aAAa;IACb,SAAS;IACT,uBAAuB;IACvB,eAAe;AACnB;;AAEA,gBAAgB;AAChB;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,+BAA+B;IAC/B,mCAAmC;IACnC,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;AACvB;;AAEA,cAAc;AACd;IACI,gBAAgB;IAChB,UAAU;IACV,gBAAgB;AACpB;;AAEA;IACI,eAAe;IACf,4CAA4C;IAC5C,aAAa;IACb,uBAAuB;IACvB,SAAS;AACb;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,YAAY;IACZ,qBAAqB;IACrB,iBAAiB;IACjB,cAAc;IACd,eAAe;AACnB;;AAEA,wBAAwB;AACxB;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,8BAA8B;IAC9B,cAAS;IAAT,SAAS;IACT,cAAc;AAClB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,+BAA+B;AACnC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,cAAc;IACd,kBAAkB;IAClB,gBAAgB;IAChB,0BAA0B;AAC9B;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,+BAA+B;IAC/B,sCAAsC;IACtC,eAAe;IACf,wDAAwD;IACxD,qBAAqB;AACzB;;AAEA;IACI,aAAa;IACb,4BAA4B;IAC5B,6CAA6C;AACjD;;AAEA;IACI,wBAAwB;AAC5B;;AAEA;IACI,gBAAgB;IAChB,iBAAiB;AACrB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,sBAAsB;IACtB,SAAS;AACb;;AAEA;IACI,aAAa;IACb,+BAA+B;IAC/B,mCAAmC;IACnC,+BAA+B;AACnC;;AAEA;IACI,mBAAmB;IACnB,qBAAqB;AACzB;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,wBAAwB;IACxB,eAAe;AACnB;;AAEA,eAAe;AACf;IACI,eAAe;IACf,+BAA+B;AACnC;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,aAAa;IACb,mCAAmC;IACnC,+BAA+B;IAC/B,yBAAyB;AAC7B;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA,kBAAkB;AAClB;IACI,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,4BAA4B;IAC5B,aAAa;IACb,mCAAmC;IACnC,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,wBAAwB;AACxB;IACI,eAAe;AACnB;;AAEA;IACI,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,+BAA+B;IAC/B,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,4BAA4B;IAC5B,aAAa;IACb,mCAAmC;IACnC,kBAAkB;IAClB,gBAAgB;IAChB,sCAAsC;AAC1C;;AAEA,sBAAsB;AACtB;IACI,eAAe;AACnB;;AAEA,sBAAsB;AACtB;IACI;QACI,iBAAiB;IACrB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,sBAAsB;QACtB,mBAAmB;IACvB;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,SAAS;IACb;;IAEA;QACI,eAAe;IACnB;;IAEA;QACI,aAAa;IACjB;;IAEA;QACI,kBAAkB;IACtB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,eAAe;IACnB;;IAEA;;;QAGI,eAAe;IACnB;AACJ;;AAEA;IACI;QACI,eAAe;IACnB;;IAEA;QACI,eAAe;IACnB;;IAEA;;QAEI,aAAa;IACjB;;IAEA;;QAEI,kBAAkB;QAClB,eAAe;IACnB;AACJ\",\"sourcesContent\":[\":root {\\n    --bg: #ffffff;\\n    --bg-secondary: #f8fafc;\\n    --bg-accent: #f0f9ff;\\n    --primary: #0ea5a4;\\n    --primary-dark: #0d9488;\\n    --primary-light: #5eead4;\\n    --secondary: #6366f1;\\n    --muted: #6b7280;\\n    --text-primary: #0f172a;\\n    --text-secondary: #475569;\\n    --text-muted: #94a3b8;\\n    --border: #e2e8f0;\\n    --border-light: #f1f5f9;\\n    --success: #10b981;\\n    --warning: #f59e0b;\\n    --error: #ef4444;\\n    --max-w: 1200px;\\n    --max-w-content: 900px;\\n    --border-radius: 12px;\\n    --border-radius-sm: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n}\\n\\n* {\\n    box-sizing: border-box;\\n}\\n\\nhtml,\\nbody,\\n#root {\\n    padding: 0;\\n    margin: 0;\\n    font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;\\n    background: var(--bg);\\n    color: var(--text-primary);\\n    line-height: 1.6;\\n}\\n\\n/* Layout */\\n.container {\\n    max-width: var(--max-w-content);\\n    margin: 0 auto;\\n    padding: 0 24px;\\n}\\n\\n.site-header {\\n    background: var(--bg);\\n    border-bottom: 1px solid var(--border);\\n    position: sticky;\\n    top: 0;\\n    z-index: 100;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n}\\n\\n.site-header .container {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 20px 24px;\\n    max-width: var(--max-w);\\n}\\n\\n.site-logo {\\n    display: flex;\\n    align-items: center;\\n    gap: 12px;\\n}\\n\\n.site-logo strong {\\n    font-size: 1.25rem;\\n    color: var(--primary);\\n}\\n\\n.nav {\\n    display: flex;\\n    gap: 32px;\\n}\\n\\n.nav a {\\n    color: var(--text-secondary);\\n    text-decoration: none;\\n    font-weight: 500;\\n    transition: color 0.2s ease;\\n    position: relative;\\n}\\n\\n.nav a:hover {\\n    color: var(--primary);\\n}\\n\\n.nav a::after {\\n    content: '';\\n    position: absolute;\\n    bottom: -4px;\\n    left: 0;\\n    width: 0;\\n    height: 2px;\\n    background: var(--primary);\\n    transition: width 0.2s ease;\\n}\\n\\n.nav a:hover::after {\\n    width: 100%;\\n}\\n\\n/* Typography */\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n    margin: 0 0 16px 0;\\n    font-weight: 700;\\n    line-height: 1.2;\\n    color: var(--text-primary);\\n}\\n\\nh1 {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n}\\n\\nh2 {\\n    font-size: 2.25rem;\\n    margin-bottom: 20px;\\n}\\n\\nh3 {\\n    font-size: 1.875rem;\\n    margin-bottom: 16px;\\n}\\n\\nh4 {\\n    font-size: 1.5rem;\\n    margin-bottom: 12px;\\n}\\n\\np {\\n    margin: 0 0 16px 0;\\n    color: var(--text-secondary);\\n}\\n\\nul,\\nol {\\n    margin: 0 0 16px 0;\\n    padding-left: 24px;\\n}\\n\\nli {\\n    margin-bottom: 8px;\\n    color: var(--text-secondary);\\n}\\n\\na {\\n    color: var(--primary);\\n    text-decoration: none;\\n    transition: color 0.2s ease;\\n}\\n\\na:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n/* Buttons */\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline {\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n    padding: 12px 24px;\\n    border-radius: var(--border-radius-sm);\\n    font-weight: 600;\\n    font-size: 16px;\\n    text-decoration: none;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n    gap: 8px;\\n}\\n\\n.btn-primary {\\n    background: var(--primary);\\n    color: white;\\n    box-shadow: var(--shadow);\\n}\\n\\n.btn-primary:hover {\\n    background: var(--primary-dark);\\n    transform: translateY(-1px);\\n    box-shadow: var(--shadow-lg);\\n    text-decoration: none;\\n    color: white;\\n}\\n\\n.btn-secondary {\\n    background: var(--bg-secondary);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border);\\n}\\n\\n.btn-secondary:hover {\\n    background: var(--border-light);\\n    transform: translateY(-1px);\\n    text-decoration: none;\\n    color: var(--text-primary);\\n}\\n\\n.btn-outline {\\n    background: transparent;\\n    color: var(--primary);\\n    border: 2px solid var(--primary);\\n}\\n\\n.btn-outline:hover {\\n    background: var(--primary);\\n    color: white;\\n    text-decoration: none;\\n}\\n\\n.btn-primary.large,\\n.btn-outline.large {\\n    padding: 16px 32px;\\n    font-size: 18px;\\n}\\n\\n/* Cards */\\n.card {\\n    background: var(--bg);\\n    border: 1px solid var(--border);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    box-shadow: var(--shadow);\\n    transition: all 0.2s ease;\\n}\\n\\n.card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n/* Hero Sections */\\n.hero {\\n    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg) 100%);\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.hero-content {\\n    max-width: 800px;\\n    margin: 0 auto;\\n}\\n\\n.hero-title {\\n    font-size: 3.5rem;\\n    margin-bottom: 24px;\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n}\\n\\n.hero-subtitle {\\n    font-size: 1.25rem;\\n    color: var(--text-secondary);\\n    margin-bottom: 40px;\\n    line-height: 1.6;\\n}\\n\\n.hero-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n.page-hero {\\n    background: var(--bg-secondary);\\n    padding: 60px 0;\\n    text-align: center;\\n    border-bottom: 1px solid var(--border);\\n}\\n\\n.last-updated {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n    margin-top: 16px;\\n}\\n\\n/* Sections */\\n.content-section {\\n    padding: 60px 0;\\n    border-bottom: 1px solid var(--border-light);\\n}\\n\\n.content-section:last-child {\\n    border-bottom: none;\\n}\\n\\n.section-header {\\n    text-align: center;\\n    margin-bottom: 60px;\\n}\\n\\n.section-subtitle {\\n    font-size: 1.125rem;\\n    color: var(--text-secondary);\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n/* Features Grid */\\n.features-section {\\n    padding: 80px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.features-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    gap: 32px;\\n    margin-top: 60px;\\n}\\n\\n.feature-card {\\n    background: var(--bg);\\n    padding: 40px 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    box-shadow: var(--shadow);\\n    transition: all 0.3s ease;\\n    border: 1px solid var(--border);\\n}\\n\\n.feature-card:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-xl);\\n}\\n\\n.feature-icon {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n    display: block;\\n}\\n\\n.feature-card h3 {\\n    color: var(--text-primary);\\n    margin-bottom: 16px;\\n}\\n\\n.feature-card p {\\n    color: var(--text-secondary);\\n    line-height: 1.6;\\n}\\n\\n/* Benefits Grid */\\n.benefits-section {\\n    padding: 80px 0;\\n}\\n\\n.benefits-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.benefit-item {\\n    text-align: center;\\n    padding: 24px;\\n}\\n\\n.benefit-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n    font-size: 1.25rem;\\n}\\n\\n/* CTA Section */\\n.cta-section {\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\\n    color: white;\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.cta-content {\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n.cta-section h2 {\\n    color: white;\\n    margin-bottom: 24px;\\n}\\n\\n.cta-section p {\\n    color: rgba(255, 255, 255, 0.9);\\n    font-size: 1.125rem;\\n    margin-bottom: 40px;\\n}\\n\\n.cta-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n/* Values Grid */\\n.values-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.value-item {\\n    padding: 32px 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n}\\n\\n.value-item h3 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Tech List */\\n.tech-list {\\n    list-style: none;\\n    padding: 0;\\n    margin-top: 32px;\\n}\\n\\n.tech-list li {\\n    padding: 16px 0;\\n    border-bottom: 1px solid var(--border-light);\\n    display: flex;\\n    align-items: flex-start;\\n    gap: 12px;\\n}\\n\\n.tech-list li:last-child {\\n    border-bottom: none;\\n}\\n\\n.tech-list li::before {\\n    content: '✓';\\n    color: var(--success);\\n    font-weight: bold;\\n    flex-shrink: 0;\\n    margin-top: 2px;\\n}\\n\\n/* Contact Page Styles */\\n.contact-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-content {\\n    display: grid;\\n    grid-template-columns: 2fr 1fr;\\n    gap: 60px;\\n    margin: 60px 0;\\n}\\n\\n.contact-form-section h2 {\\n    margin-bottom: 16px;\\n}\\n\\n.contact-form {\\n    background: var(--bg-secondary);\\n    padding: 40px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.form-group {\\n    margin-bottom: 24px;\\n}\\n\\n.form-group label {\\n    display: block;\\n    margin-bottom: 8px;\\n    font-weight: 600;\\n    color: var(--text-primary);\\n}\\n\\n.form-input {\\n    width: 100%;\\n    padding: 12px 16px;\\n    border: 1px solid var(--border);\\n    border-radius: var(--border-radius-sm);\\n    font-size: 16px;\\n    transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n    background: var(--bg);\\n}\\n\\n.form-input:focus {\\n    outline: none;\\n    border-color: var(--primary);\\n    box-shadow: 0 0 0 3px rgba(14, 165, 164, 0.1);\\n}\\n\\n.form-input::placeholder {\\n    color: var(--text-muted);\\n}\\n\\ntextarea.form-input {\\n    resize: vertical;\\n    min-height: 120px;\\n}\\n\\n.contact-info-section {\\n    padding: 40px 0;\\n}\\n\\n.contact-methods {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 32px;\\n}\\n\\n.contact-method {\\n    padding: 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.contact-method h3 {\\n    margin-bottom: 12px;\\n    color: var(--primary);\\n}\\n\\n.contact-method p {\\n    margin-bottom: 8px;\\n}\\n\\n.contact-method small {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n}\\n\\n/* FAQ Styles */\\n.faq-section {\\n    padding: 60px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.faq-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    gap: 24px;\\n    margin-top: 40px;\\n}\\n\\n.faq-item {\\n    background: var(--bg);\\n    padding: 24px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n    transition: all 0.2s ease;\\n}\\n\\n.faq-item:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n.faq-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 12px;\\n    font-size: 1.125rem;\\n}\\n\\n.faq-item p {\\n    line-height: 1.6;\\n    margin-bottom: 0;\\n}\\n\\n/* Support Hours */\\n.support-hours {\\n    padding: 40px 0;\\n    text-align: center;\\n}\\n\\n.hours-info {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    margin-top: 24px;\\n}\\n\\n.hours-info p {\\n    margin-bottom: 12px;\\n}\\n\\n/* Privacy Page Styles */\\n.privacy-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-info {\\n    background: var(--bg-secondary);\\n    padding: 24px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n    margin-top: 24px;\\n}\\n\\n.contact-info p {\\n    margin-bottom: 8px;\\n}\\n\\n.effective-date {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    margin-top: 60px;\\n    border: 1px solid var(--primary-light);\\n}\\n\\n/* About Page Styles */\\n.about-page {\\n    padding: 40px 0;\\n}\\n\\n/* Responsive Design */\\n@media (max-width: 768px) {\\n    .hero-title {\\n        font-size: 2.5rem;\\n    }\\n\\n    .hero-subtitle {\\n        font-size: 1.125rem;\\n    }\\n\\n    .hero-buttons {\\n        flex-direction: column;\\n        align-items: center;\\n    }\\n\\n    .features-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .benefits-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .values-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .contact-content {\\n        grid-template-columns: 1fr;\\n        gap: 40px;\\n    }\\n\\n    .faq-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .nav {\\n        gap: 16px;\\n    }\\n\\n    .container {\\n        padding: 0 16px;\\n    }\\n\\n    .site-header .container {\\n        padding: 16px;\\n    }\\n\\n    h1 {\\n        font-size: 2.25rem;\\n    }\\n\\n    h2 {\\n        font-size: 1.875rem;\\n    }\\n\\n    .content-section {\\n        padding: 40px 0;\\n    }\\n\\n    .features-section,\\n    .benefits-section,\\n    .cta-section {\\n        padding: 60px 0;\\n    }\\n}\\n\\n@media (max-width: 480px) {\\n    .hero-title {\\n        font-size: 2rem;\\n    }\\n\\n    .hero {\\n        padding: 60px 0;\\n    }\\n\\n    .feature-card,\\n    .contact-form {\\n        padding: 24px;\\n    }\\n\\n    .btn-primary.large,\\n    .btn-outline.large {\\n        padding: 14px 24px;\\n        font-size: 16px;\\n    }\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});