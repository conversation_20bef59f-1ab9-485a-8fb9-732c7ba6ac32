"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \":root {\\n    --bg: #ffffff;\\n    --bg-secondary: #f8fafc;\\n    --bg-accent: #f0f9ff;\\n    --primary: #0ea5a4;\\n    --primary-dark: #0d9488;\\n    --primary-light: #5eead4;\\n    --secondary: #6366f1;\\n    --muted: #6b7280;\\n    --text-primary: #0f172a;\\n    --text-secondary: #475569;\\n    --text-muted: #94a3b8;\\n    --border: #e2e8f0;\\n    --border-light: #f1f5f9;\\n    --success: #10b981;\\n    --warning: #f59e0b;\\n    --error: #ef4444;\\n    --max-w: 1200px;\\n    --max-w-content: 900px;\\n    --border-radius: 12px;\\n    --border-radius-sm: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n}\\n\\n* {\\n    box-sizing: border-box;\\n}\\n\\nhtml,\\nbody,\\n#root {\\n    padding: 0;\\n    margin: 0;\\n    font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;\\n    background: var(--bg);\\n    color: var(--text-primary);\\n    line-height: 1.6;\\n}\\n\\n/* Layout */\\n.container {\\n    max-width: var(--max-w-content);\\n    margin: 0 auto;\\n    padding: 0 24px;\\n}\\n\\n.site-header {\\n    background: var(--bg);\\n    border-bottom: 1px solid var(--border);\\n    position: -webkit-sticky;\\n    position: sticky;\\n    top: 0;\\n    z-index: 100;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n}\\n\\n.site-header .container {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 20px 24px;\\n    max-width: var(--max-w);\\n}\\n\\n.site-logo {\\n    display: flex;\\n    align-items: center;\\n    gap: 12px;\\n}\\n\\n.site-logo strong {\\n    font-size: 1.25rem;\\n    color: var(--primary);\\n}\\n\\n.nav {\\n    display: flex;\\n    gap: 32px;\\n}\\n\\n.nav a {\\n    color: var(--text-secondary);\\n    text-decoration: none;\\n    font-weight: 500;\\n    transition: color 0.2s ease;\\n    position: relative;\\n}\\n\\n.nav a:hover {\\n    color: var(--primary);\\n}\\n\\n.nav a::after {\\n    content: '';\\n    position: absolute;\\n    bottom: -4px;\\n    left: 0;\\n    width: 0;\\n    height: 2px;\\n    background: var(--primary);\\n    transition: width 0.2s ease;\\n}\\n\\n.nav a:hover::after {\\n    width: 100%;\\n}\\n\\n/* Typography */\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n    margin: 0 0 16px 0;\\n    font-weight: 700;\\n    line-height: 1.2;\\n    color: var(--text-primary);\\n}\\n\\nh1 {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n}\\n\\nh2 {\\n    font-size: 2.25rem;\\n    margin-bottom: 20px;\\n}\\n\\nh3 {\\n    font-size: 1.875rem;\\n    margin-bottom: 16px;\\n}\\n\\nh4 {\\n    font-size: 1.5rem;\\n    margin-bottom: 12px;\\n}\\n\\np {\\n    margin: 0 0 16px 0;\\n    color: var(--text-secondary);\\n}\\n\\nul,\\nol {\\n    margin: 0 0 16px 0;\\n    padding-left: 24px;\\n}\\n\\nli {\\n    margin-bottom: 8px;\\n    color: var(--text-secondary);\\n}\\n\\na {\\n    color: var(--primary);\\n    text-decoration: none;\\n    transition: color 0.2s ease;\\n}\\n\\na:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n/* Buttons */\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline {\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n    padding: 12px 24px;\\n    border-radius: var(--border-radius-sm);\\n    font-weight: 600;\\n    font-size: 16px;\\n    text-decoration: none;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n    gap: 8px;\\n}\\n\\n.btn-primary {\\n    background: var(--primary);\\n    color: white;\\n    box-shadow: var(--shadow);\\n}\\n\\n.btn-primary:hover {\\n    background: var(--primary-dark);\\n    transform: translateY(-1px);\\n    box-shadow: var(--shadow-lg);\\n    text-decoration: none;\\n    color: white;\\n}\\n\\n.btn-secondary {\\n    background: var(--bg-secondary);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border);\\n}\\n\\n.btn-secondary:hover {\\n    background: var(--border-light);\\n    transform: translateY(-1px);\\n    text-decoration: none;\\n    color: var(--text-primary);\\n}\\n\\n.btn-outline {\\n    background: transparent;\\n    color: var(--primary);\\n    border: 2px solid var(--primary);\\n}\\n\\n.btn-outline:hover {\\n    background: var(--primary);\\n    color: white;\\n    text-decoration: none;\\n}\\n\\n.btn-primary.large,\\n.btn-outline.large {\\n    padding: 16px 32px;\\n    font-size: 18px;\\n}\\n\\n/* Cards */\\n.card {\\n    background: var(--bg);\\n    border: 1px solid var(--border);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    box-shadow: var(--shadow);\\n    transition: all 0.2s ease;\\n}\\n\\n.card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n/* Hero Sections */\\n.hero {\\n    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg) 100%);\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.hero-content {\\n    max-width: 800px;\\n    margin: 0 auto;\\n}\\n\\n.hero-title {\\n    font-size: 3.5rem;\\n    margin-bottom: 24px;\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n}\\n\\n.hero-subtitle {\\n    font-size: 1.25rem;\\n    color: var(--text-secondary);\\n    margin-bottom: 40px;\\n    line-height: 1.6;\\n}\\n\\n.hero-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n.page-hero {\\n    background: var(--bg-secondary);\\n    padding: 60px 0;\\n    text-align: center;\\n    border-bottom: 1px solid var(--border);\\n}\\n\\n.last-updated {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n    margin-top: 16px;\\n}\\n\\n/* Sections */\\n.content-section {\\n    padding: 60px 0;\\n    border-bottom: 1px solid var(--border-light);\\n}\\n\\n.content-section:last-child {\\n    border-bottom: none;\\n}\\n\\n.section-header {\\n    text-align: center;\\n    margin-bottom: 60px;\\n}\\n\\n.section-subtitle {\\n    font-size: 1.125rem;\\n    color: var(--text-secondary);\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n/* Features Grid */\\n.features-section {\\n    padding: 80px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.features-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 60px;\\n}\\n\\n.feature-card {\\n    background: var(--bg);\\n    padding: 40px 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    box-shadow: var(--shadow);\\n    transition: all 0.3s ease;\\n    border: 1px solid var(--border);\\n}\\n\\n.feature-card:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-xl);\\n}\\n\\n.feature-icon {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n    display: block;\\n}\\n\\n.feature-card h3 {\\n    color: var(--text-primary);\\n    margin-bottom: 16px;\\n}\\n\\n.feature-card p {\\n    color: var(--text-secondary);\\n    line-height: 1.6;\\n}\\n\\n/* Benefits Grid */\\n.benefits-section {\\n    padding: 80px 0;\\n}\\n\\n.benefits-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.benefit-item {\\n    text-align: center;\\n    padding: 24px;\\n}\\n\\n.benefit-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n    font-size: 1.25rem;\\n}\\n\\n/* CTA Section */\\n.cta-section {\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\\n    color: white;\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.cta-content {\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n.cta-section h2 {\\n    color: white;\\n    margin-bottom: 24px;\\n}\\n\\n.cta-section p {\\n    color: rgba(255, 255, 255, 0.9);\\n    font-size: 1.125rem;\\n    margin-bottom: 40px;\\n}\\n\\n.cta-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n/* Values Grid */\\n.values-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.value-item {\\n    padding: 32px 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n}\\n\\n.value-item h3 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Tech List */\\n.tech-list {\\n    list-style: none;\\n    padding: 0;\\n    margin-top: 32px;\\n}\\n\\n.tech-list li {\\n    padding: 16px 0;\\n    border-bottom: 1px solid var(--border-light);\\n    display: flex;\\n    align-items: flex-start;\\n    gap: 12px;\\n}\\n\\n.tech-list li:last-child {\\n    border-bottom: none;\\n}\\n\\n.tech-list li::before {\\n    content: '✓';\\n    color: var(--success);\\n    font-weight: bold;\\n    flex-shrink: 0;\\n    margin-top: 2px;\\n}\\n\\n/* Contact Page Styles */\\n.contact-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-content {\\n    display: grid;\\n    grid-template-columns: 2fr 1fr;\\n    grid-gap: 60px;\\n    gap: 60px;\\n    margin: 60px 0;\\n}\\n\\n.contact-form-section h2 {\\n    margin-bottom: 16px;\\n}\\n\\n.contact-form {\\n    background: var(--bg-secondary);\\n    padding: 40px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.form-group {\\n    margin-bottom: 24px;\\n}\\n\\n.form-group label {\\n    display: block;\\n    margin-bottom: 8px;\\n    font-weight: 600;\\n    color: var(--text-primary);\\n}\\n\\n.form-input {\\n    width: 100%;\\n    padding: 12px 16px;\\n    border: 1px solid var(--border);\\n    border-radius: var(--border-radius-sm);\\n    font-size: 16px;\\n    transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n    background: var(--bg);\\n}\\n\\n.form-input:focus {\\n    outline: none;\\n    border-color: var(--primary);\\n    box-shadow: 0 0 0 3px rgba(14, 165, 164, 0.1);\\n}\\n\\n.form-input::placeholder {\\n    color: var(--text-muted);\\n}\\n\\ntextarea.form-input {\\n    resize: vertical;\\n    min-height: 120px;\\n}\\n\\n.contact-info-section {\\n    padding: 40px 0;\\n}\\n\\n.contact-methods {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 32px;\\n}\\n\\n.contact-method {\\n    padding: 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.contact-method h3 {\\n    margin-bottom: 12px;\\n    color: var(--primary);\\n}\\n\\n.contact-method p {\\n    margin-bottom: 8px;\\n}\\n\\n.contact-method small {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n}\\n\\n/* FAQ Styles */\\n.faq-section {\\n    padding: 60px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.faq-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.faq-item {\\n    background: var(--bg);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.faq-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Support Hours */\\n.support-hours {\\n    padding: 40px 0;\\n    text-align: center;\\n}\\n\\n.hours-info {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    margin-top: 24px;\\n}\\n\\n.hours-info p {\\n    margin-bottom: 12px;\\n}\\n\\n/* Privacy Page Styles */\\n.privacy-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-info {\\n    background: var(--bg-secondary);\\n    padding: 24px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n    margin-top: 24px;\\n}\\n\\n.contact-info p {\\n    margin-bottom: 8px;\\n}\\n\\n.effective-date {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    margin-top: 60px;\\n    border: 1px solid var(--primary-light);\\n}\\n\\n/* About Page Styles */\\n.about-page {\\n    padding: 40px 0;\\n}\\n\\n/* Responsive Design */\\n@media (max-width: 768px) {\\n    .hero-title {\\n        font-size: 2.5rem;\\n    }\\n\\n    .hero-subtitle {\\n        font-size: 1.125rem;\\n    }\\n\\n    .hero-buttons {\\n        flex-direction: column;\\n        align-items: center;\\n    }\\n\\n    .features-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .benefits-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .values-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .contact-content {\\n        grid-template-columns: 1fr;\\n        gap: 40px;\\n    }\\n\\n    .faq-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .nav {\\n        gap: 16px;\\n    }\\n\\n    .container {\\n        padding: 0 16px;\\n    }\\n\\n    .site-header .container {\\n        padding: 16px;\\n    }\\n\\n    h1 {\\n        font-size: 2.25rem;\\n    }\\n\\n    h2 {\\n        font-size: 1.875rem;\\n    }\\n\\n    .content-section {\\n        padding: 40px 0;\\n    }\\n\\n    .features-section,\\n    .benefits-section,\\n    .cta-section {\\n        padding: 60px 0;\\n    }\\n}\\n\\n@media (max-width: 480px) {\\n    .hero-title {\\n        font-size: 2rem;\\n    }\\n\\n    .hero {\\n        padding: 60px 0;\\n    }\\n\\n    .feature-card,\\n    .contact-form {\\n        padding: 24px;\\n    }\\n\\n    .btn-primary.large,\\n    .btn-outline.large {\\n        padding: 14px 24px;\\n        font-size: 16px;\\n    }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;IACI,aAAa;IACb,uBAAuB;IACvB,oBAAoB;IACpB,kBAAkB;IAClB,uBAAuB;IACvB,wBAAwB;IACxB,oBAAoB;IACpB,gBAAgB;IAChB,uBAAuB;IACvB,yBAAyB;IACzB,qBAAqB;IACrB,iBAAiB;IACjB,uBAAuB;IACvB,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,sBAAsB;IACtB,qBAAqB;IACrB,uBAAuB;IACvB,uEAAuE;IACvE,+EAA+E;IAC/E,gFAAgF;AACpF;;AAEA;IACI,sBAAsB;AAC1B;;AAEA;;;IAGI,UAAU;IACV,SAAS;IACT,yFAAyF;IACzF,qBAAqB;IACrB,0BAA0B;IAC1B,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,+BAA+B;IAC/B,cAAc;IACd,eAAe;AACnB;;AAEA;IACI,qBAAqB;IACrB,sCAAsC;IACtC,wBAAgB;IAAhB,gBAAgB;IAChB,MAAM;IACN,YAAY;IACZ,mCAAmC;IACnC,2BAA2B;AAC/B;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,8BAA8B;IAC9B,kBAAkB;IAClB,uBAAuB;AAC3B;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,SAAS;AACb;;AAEA;IACI,kBAAkB;IAClB,qBAAqB;AACzB;;AAEA;IACI,aAAa;IACb,SAAS;AACb;;AAEA;IACI,4BAA4B;IAC5B,qBAAqB;IACrB,gBAAgB;IAChB,2BAA2B;IAC3B,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;AACzB;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,WAAW;IACX,0BAA0B;IAC1B,2BAA2B;AAC/B;;AAEA;IACI,WAAW;AACf;;AAEA,eAAe;AACf;;;;;;IAMI,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,0BAA0B;AAC9B;;AAEA;IACI,eAAe;IACf,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;AAChC;;AAEA;;IAEI,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;AAChC;;AAEA;IACI,qBAAqB;IACrB,qBAAqB;IACrB,2BAA2B;AAC/B;;AAEA;IACI,0BAA0B;IAC1B,0BAA0B;AAC9B;;AAEA,YAAY;AACZ;;;IAGI,oBAAoB;IACpB,mBAAmB;IACnB,uBAAuB;IACvB,kBAAkB;IAClB,sCAAsC;IACtC,gBAAgB;IAChB,eAAe;IACf,qBAAqB;IACrB,YAAY;IACZ,eAAe;IACf,yBAAyB;IACzB,QAAQ;AACZ;;AAEA;IACI,0BAA0B;IAC1B,YAAY;IACZ,yBAAyB;AAC7B;;AAEA;IACI,+BAA+B;IAC/B,2BAA2B;IAC3B,4BAA4B;IAC5B,qBAAqB;IACrB,YAAY;AAChB;;AAEA;IACI,+BAA+B;IAC/B,0BAA0B;IAC1B,+BAA+B;AACnC;;AAEA;IACI,+BAA+B;IAC/B,2BAA2B;IAC3B,qBAAqB;IACrB,0BAA0B;AAC9B;;AAEA;IACI,uBAAuB;IACvB,qBAAqB;IACrB,gCAAgC;AACpC;;AAEA;IACI,0BAA0B;IAC1B,YAAY;IACZ,qBAAqB;AACzB;;AAEA;;IAEI,kBAAkB;IAClB,eAAe;AACnB;;AAEA,UAAU;AACV;IACI,qBAAqB;IACrB,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,yBAAyB;IACzB,yBAAyB;AAC7B;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA,kBAAkB;AAClB;IACI,wEAAwE;IACxE,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,cAAc;AAClB;;AAEA;IACI,iBAAiB;IACjB,mBAAmB;IACnB,6EAA6E;IAC7E,6BAA6B;IAC7B,oCAAoC;IACpC,qBAAqB;AACzB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;IAC5B,mBAAmB;IACnB,gBAAgB;AACpB;;AAEA;IACI,aAAa;IACb,SAAS;IACT,uBAAuB;IACvB,eAAe;AACnB;;AAEA;IACI,+BAA+B;IAC/B,eAAe;IACf,kBAAkB;IAClB,sCAAsC;AAC1C;;AAEA;IACI,wBAAwB;IACxB,eAAe;IACf,gBAAgB;AACpB;;AAEA,aAAa;AACb;IACI,eAAe;IACf,4CAA4C;AAChD;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,4BAA4B;IAC5B,gBAAgB;IAChB,cAAc;AAClB;;AAEA,kBAAkB;AAClB;IACI,eAAe;IACf,+BAA+B;AACnC;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,kBAAkB;IAClB,mCAAmC;IACnC,kBAAkB;IAClB,yBAAyB;IACzB,yBAAyB;IACzB,+BAA+B;AACnC;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA;IACI,eAAe;IACf,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,0BAA0B;IAC1B,mBAAmB;AACvB;;AAEA;IACI,4BAA4B;IAC5B,gBAAgB;AACpB;;AAEA,kBAAkB;AAClB;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,aAAa;AACjB;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA,gBAAgB;AAChB;IACI,gFAAgF;IAChF,YAAY;IACZ,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,cAAc;AAClB;;AAEA;IACI,YAAY;IACZ,mBAAmB;AACvB;;AAEA;IACI,+BAA+B;IAC/B,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,aAAa;IACb,SAAS;IACT,uBAAuB;IACvB,eAAe;AACnB;;AAEA,gBAAgB;AAChB;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,+BAA+B;IAC/B,mCAAmC;IACnC,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;AACvB;;AAEA,cAAc;AACd;IACI,gBAAgB;IAChB,UAAU;IACV,gBAAgB;AACpB;;AAEA;IACI,eAAe;IACf,4CAA4C;IAC5C,aAAa;IACb,uBAAuB;IACvB,SAAS;AACb;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,YAAY;IACZ,qBAAqB;IACrB,iBAAiB;IACjB,cAAc;IACd,eAAe;AACnB;;AAEA,wBAAwB;AACxB;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,8BAA8B;IAC9B,cAAS;IAAT,SAAS;IACT,cAAc;AAClB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,+BAA+B;AACnC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,cAAc;IACd,kBAAkB;IAClB,gBAAgB;IAChB,0BAA0B;AAC9B;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,+BAA+B;IAC/B,sCAAsC;IACtC,eAAe;IACf,wDAAwD;IACxD,qBAAqB;AACzB;;AAEA;IACI,aAAa;IACb,4BAA4B;IAC5B,6CAA6C;AACjD;;AAEA;IACI,wBAAwB;AAC5B;;AAEA;IACI,gBAAgB;IAChB,iBAAiB;AACrB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,sBAAsB;IACtB,SAAS;AACb;;AAEA;IACI,aAAa;IACb,+BAA+B;IAC/B,mCAAmC;IACnC,+BAA+B;AACnC;;AAEA;IACI,mBAAmB;IACnB,qBAAqB;AACzB;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,wBAAwB;IACxB,eAAe;AACnB;;AAEA,eAAe;AACf;IACI,eAAe;IACf,+BAA+B;AACnC;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,aAAa;IACb,mCAAmC;IACnC,+BAA+B;AACnC;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;AACvB;;AAEA,kBAAkB;AAClB;IACI,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,4BAA4B;IAC5B,aAAa;IACb,mCAAmC;IACnC,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,wBAAwB;AACxB;IACI,eAAe;AACnB;;AAEA;IACI,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,+BAA+B;IAC/B,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,4BAA4B;IAC5B,aAAa;IACb,mCAAmC;IACnC,kBAAkB;IAClB,gBAAgB;IAChB,sCAAsC;AAC1C;;AAEA,sBAAsB;AACtB;IACI,eAAe;AACnB;;AAEA,sBAAsB;AACtB;IACI;QACI,iBAAiB;IACrB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,sBAAsB;QACtB,mBAAmB;IACvB;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,SAAS;IACb;;IAEA;QACI,eAAe;IACnB;;IAEA;QACI,aAAa;IACjB;;IAEA;QACI,kBAAkB;IACtB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,eAAe;IACnB;;IAEA;;;QAGI,eAAe;IACnB;AACJ;;AAEA;IACI;QACI,eAAe;IACnB;;IAEA;QACI,eAAe;IACnB;;IAEA;;QAEI,aAAa;IACjB;;IAEA;;QAEI,kBAAkB;QAClB,eAAe;IACnB;AACJ\",\"sourcesContent\":[\":root {\\n    --bg: #ffffff;\\n    --bg-secondary: #f8fafc;\\n    --bg-accent: #f0f9ff;\\n    --primary: #0ea5a4;\\n    --primary-dark: #0d9488;\\n    --primary-light: #5eead4;\\n    --secondary: #6366f1;\\n    --muted: #6b7280;\\n    --text-primary: #0f172a;\\n    --text-secondary: #475569;\\n    --text-muted: #94a3b8;\\n    --border: #e2e8f0;\\n    --border-light: #f1f5f9;\\n    --success: #10b981;\\n    --warning: #f59e0b;\\n    --error: #ef4444;\\n    --max-w: 1200px;\\n    --max-w-content: 900px;\\n    --border-radius: 12px;\\n    --border-radius-sm: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n}\\n\\n* {\\n    box-sizing: border-box;\\n}\\n\\nhtml,\\nbody,\\n#root {\\n    padding: 0;\\n    margin: 0;\\n    font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;\\n    background: var(--bg);\\n    color: var(--text-primary);\\n    line-height: 1.6;\\n}\\n\\n/* Layout */\\n.container {\\n    max-width: var(--max-w-content);\\n    margin: 0 auto;\\n    padding: 0 24px;\\n}\\n\\n.site-header {\\n    background: var(--bg);\\n    border-bottom: 1px solid var(--border);\\n    position: sticky;\\n    top: 0;\\n    z-index: 100;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n}\\n\\n.site-header .container {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 20px 24px;\\n    max-width: var(--max-w);\\n}\\n\\n.site-logo {\\n    display: flex;\\n    align-items: center;\\n    gap: 12px;\\n}\\n\\n.site-logo strong {\\n    font-size: 1.25rem;\\n    color: var(--primary);\\n}\\n\\n.nav {\\n    display: flex;\\n    gap: 32px;\\n}\\n\\n.nav a {\\n    color: var(--text-secondary);\\n    text-decoration: none;\\n    font-weight: 500;\\n    transition: color 0.2s ease;\\n    position: relative;\\n}\\n\\n.nav a:hover {\\n    color: var(--primary);\\n}\\n\\n.nav a::after {\\n    content: '';\\n    position: absolute;\\n    bottom: -4px;\\n    left: 0;\\n    width: 0;\\n    height: 2px;\\n    background: var(--primary);\\n    transition: width 0.2s ease;\\n}\\n\\n.nav a:hover::after {\\n    width: 100%;\\n}\\n\\n/* Typography */\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n    margin: 0 0 16px 0;\\n    font-weight: 700;\\n    line-height: 1.2;\\n    color: var(--text-primary);\\n}\\n\\nh1 {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n}\\n\\nh2 {\\n    font-size: 2.25rem;\\n    margin-bottom: 20px;\\n}\\n\\nh3 {\\n    font-size: 1.875rem;\\n    margin-bottom: 16px;\\n}\\n\\nh4 {\\n    font-size: 1.5rem;\\n    margin-bottom: 12px;\\n}\\n\\np {\\n    margin: 0 0 16px 0;\\n    color: var(--text-secondary);\\n}\\n\\nul,\\nol {\\n    margin: 0 0 16px 0;\\n    padding-left: 24px;\\n}\\n\\nli {\\n    margin-bottom: 8px;\\n    color: var(--text-secondary);\\n}\\n\\na {\\n    color: var(--primary);\\n    text-decoration: none;\\n    transition: color 0.2s ease;\\n}\\n\\na:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n/* Buttons */\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline {\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n    padding: 12px 24px;\\n    border-radius: var(--border-radius-sm);\\n    font-weight: 600;\\n    font-size: 16px;\\n    text-decoration: none;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n    gap: 8px;\\n}\\n\\n.btn-primary {\\n    background: var(--primary);\\n    color: white;\\n    box-shadow: var(--shadow);\\n}\\n\\n.btn-primary:hover {\\n    background: var(--primary-dark);\\n    transform: translateY(-1px);\\n    box-shadow: var(--shadow-lg);\\n    text-decoration: none;\\n    color: white;\\n}\\n\\n.btn-secondary {\\n    background: var(--bg-secondary);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border);\\n}\\n\\n.btn-secondary:hover {\\n    background: var(--border-light);\\n    transform: translateY(-1px);\\n    text-decoration: none;\\n    color: var(--text-primary);\\n}\\n\\n.btn-outline {\\n    background: transparent;\\n    color: var(--primary);\\n    border: 2px solid var(--primary);\\n}\\n\\n.btn-outline:hover {\\n    background: var(--primary);\\n    color: white;\\n    text-decoration: none;\\n}\\n\\n.btn-primary.large,\\n.btn-outline.large {\\n    padding: 16px 32px;\\n    font-size: 18px;\\n}\\n\\n/* Cards */\\n.card {\\n    background: var(--bg);\\n    border: 1px solid var(--border);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    box-shadow: var(--shadow);\\n    transition: all 0.2s ease;\\n}\\n\\n.card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n/* Hero Sections */\\n.hero {\\n    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg) 100%);\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.hero-content {\\n    max-width: 800px;\\n    margin: 0 auto;\\n}\\n\\n.hero-title {\\n    font-size: 3.5rem;\\n    margin-bottom: 24px;\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n}\\n\\n.hero-subtitle {\\n    font-size: 1.25rem;\\n    color: var(--text-secondary);\\n    margin-bottom: 40px;\\n    line-height: 1.6;\\n}\\n\\n.hero-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n.page-hero {\\n    background: var(--bg-secondary);\\n    padding: 60px 0;\\n    text-align: center;\\n    border-bottom: 1px solid var(--border);\\n}\\n\\n.last-updated {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n    margin-top: 16px;\\n}\\n\\n/* Sections */\\n.content-section {\\n    padding: 60px 0;\\n    border-bottom: 1px solid var(--border-light);\\n}\\n\\n.content-section:last-child {\\n    border-bottom: none;\\n}\\n\\n.section-header {\\n    text-align: center;\\n    margin-bottom: 60px;\\n}\\n\\n.section-subtitle {\\n    font-size: 1.125rem;\\n    color: var(--text-secondary);\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n/* Features Grid */\\n.features-section {\\n    padding: 80px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.features-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    gap: 32px;\\n    margin-top: 60px;\\n}\\n\\n.feature-card {\\n    background: var(--bg);\\n    padding: 40px 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    box-shadow: var(--shadow);\\n    transition: all 0.3s ease;\\n    border: 1px solid var(--border);\\n}\\n\\n.feature-card:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-xl);\\n}\\n\\n.feature-icon {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n    display: block;\\n}\\n\\n.feature-card h3 {\\n    color: var(--text-primary);\\n    margin-bottom: 16px;\\n}\\n\\n.feature-card p {\\n    color: var(--text-secondary);\\n    line-height: 1.6;\\n}\\n\\n/* Benefits Grid */\\n.benefits-section {\\n    padding: 80px 0;\\n}\\n\\n.benefits-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.benefit-item {\\n    text-align: center;\\n    padding: 24px;\\n}\\n\\n.benefit-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n    font-size: 1.25rem;\\n}\\n\\n/* CTA Section */\\n.cta-section {\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\\n    color: white;\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.cta-content {\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n.cta-section h2 {\\n    color: white;\\n    margin-bottom: 24px;\\n}\\n\\n.cta-section p {\\n    color: rgba(255, 255, 255, 0.9);\\n    font-size: 1.125rem;\\n    margin-bottom: 40px;\\n}\\n\\n.cta-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n/* Values Grid */\\n.values-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.value-item {\\n    padding: 32px 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n}\\n\\n.value-item h3 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Tech List */\\n.tech-list {\\n    list-style: none;\\n    padding: 0;\\n    margin-top: 32px;\\n}\\n\\n.tech-list li {\\n    padding: 16px 0;\\n    border-bottom: 1px solid var(--border-light);\\n    display: flex;\\n    align-items: flex-start;\\n    gap: 12px;\\n}\\n\\n.tech-list li:last-child {\\n    border-bottom: none;\\n}\\n\\n.tech-list li::before {\\n    content: '✓';\\n    color: var(--success);\\n    font-weight: bold;\\n    flex-shrink: 0;\\n    margin-top: 2px;\\n}\\n\\n/* Contact Page Styles */\\n.contact-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-content {\\n    display: grid;\\n    grid-template-columns: 2fr 1fr;\\n    gap: 60px;\\n    margin: 60px 0;\\n}\\n\\n.contact-form-section h2 {\\n    margin-bottom: 16px;\\n}\\n\\n.contact-form {\\n    background: var(--bg-secondary);\\n    padding: 40px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.form-group {\\n    margin-bottom: 24px;\\n}\\n\\n.form-group label {\\n    display: block;\\n    margin-bottom: 8px;\\n    font-weight: 600;\\n    color: var(--text-primary);\\n}\\n\\n.form-input {\\n    width: 100%;\\n    padding: 12px 16px;\\n    border: 1px solid var(--border);\\n    border-radius: var(--border-radius-sm);\\n    font-size: 16px;\\n    transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n    background: var(--bg);\\n}\\n\\n.form-input:focus {\\n    outline: none;\\n    border-color: var(--primary);\\n    box-shadow: 0 0 0 3px rgba(14, 165, 164, 0.1);\\n}\\n\\n.form-input::placeholder {\\n    color: var(--text-muted);\\n}\\n\\ntextarea.form-input {\\n    resize: vertical;\\n    min-height: 120px;\\n}\\n\\n.contact-info-section {\\n    padding: 40px 0;\\n}\\n\\n.contact-methods {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 32px;\\n}\\n\\n.contact-method {\\n    padding: 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.contact-method h3 {\\n    margin-bottom: 12px;\\n    color: var(--primary);\\n}\\n\\n.contact-method p {\\n    margin-bottom: 8px;\\n}\\n\\n.contact-method small {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n}\\n\\n/* FAQ Styles */\\n.faq-section {\\n    padding: 60px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.faq-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.faq-item {\\n    background: var(--bg);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.faq-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Support Hours */\\n.support-hours {\\n    padding: 40px 0;\\n    text-align: center;\\n}\\n\\n.hours-info {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    margin-top: 24px;\\n}\\n\\n.hours-info p {\\n    margin-bottom: 12px;\\n}\\n\\n/* Privacy Page Styles */\\n.privacy-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-info {\\n    background: var(--bg-secondary);\\n    padding: 24px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n    margin-top: 24px;\\n}\\n\\n.contact-info p {\\n    margin-bottom: 8px;\\n}\\n\\n.effective-date {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    margin-top: 60px;\\n    border: 1px solid var(--primary-light);\\n}\\n\\n/* About Page Styles */\\n.about-page {\\n    padding: 40px 0;\\n}\\n\\n/* Responsive Design */\\n@media (max-width: 768px) {\\n    .hero-title {\\n        font-size: 2.5rem;\\n    }\\n\\n    .hero-subtitle {\\n        font-size: 1.125rem;\\n    }\\n\\n    .hero-buttons {\\n        flex-direction: column;\\n        align-items: center;\\n    }\\n\\n    .features-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .benefits-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .values-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .contact-content {\\n        grid-template-columns: 1fr;\\n        gap: 40px;\\n    }\\n\\n    .faq-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .nav {\\n        gap: 16px;\\n    }\\n\\n    .container {\\n        padding: 0 16px;\\n    }\\n\\n    .site-header .container {\\n        padding: 16px;\\n    }\\n\\n    h1 {\\n        font-size: 2.25rem;\\n    }\\n\\n    h2 {\\n        font-size: 1.875rem;\\n    }\\n\\n    .content-section {\\n        padding: 40px 0;\\n    }\\n\\n    .features-section,\\n    .benefits-section,\\n    .cta-section {\\n        padding: 60px 0;\\n    }\\n}\\n\\n@media (max-width: 480px) {\\n    .hero-title {\\n        font-size: 2rem;\\n    }\\n\\n    .hero {\\n        padding: 60px 0;\\n    }\\n\\n    .feature-card,\\n    .contact-form {\\n        padding: 24px;\\n    }\\n\\n    .btn-primary.large,\\n    .btn-outline.large {\\n        padding: 14px 24px;\\n        font-size: 16px;\\n    }\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});