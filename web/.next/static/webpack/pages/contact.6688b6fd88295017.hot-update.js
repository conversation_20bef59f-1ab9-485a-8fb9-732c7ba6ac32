"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/contact",{

/***/ "./pages/contact.tsx":
/*!***************************!*\
  !*** ./pages/contact.tsx ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Contact; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.tsx\");\n\n\nfunction Contact() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"contact-page\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"page-hero\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            children: \"Contact & Support\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hero-subtitle\",\n                            children: \"We're here to help you get the most out of FamilyMedManager\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 11,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 9,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"contact-info-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Get in Touch\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 18,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Have a question, suggestion, or need support? We'd love to hear from you.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 19,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"contact-methods\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-method\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"\\uD83D\\uDCE7 General Contact\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 23,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"For all inquiries, support, and feedback:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 24,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 25,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 22,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-method\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"\\uD83D\\uDEA8 Technical Support\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"For urgent technical issues and bug reports:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 30,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 31,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                            children: 'Please include \"URGENT\" in the subject line for priority support'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 32,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 28,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"contact-method\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            children: \"\\uD83D\\uDCA1 Feature Requests\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 36,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Have an idea to improve FamilyMedManager?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 37,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"mailto:<EMAIL>\",\n                                            children: \"<EMAIL>\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 38,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"small\", {\n                                            children: \"We love hearing from our users about new features!\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 39,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 21,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 17,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"faq-section\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Frequently Asked Questions\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 46,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"faq-grid\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How secure is my family's health data?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 49,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Your data is stored locally on your device using SQLite database with offline-first architecture. We don't store your personal health information on our servers.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 50,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 48,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I use FamilyMedManager on multiple devices?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 57,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! FamilyMedManager works on iOS, Android, and Web platforms. Data synchronization features are available to keep your information consistent across devices.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Is there a cost to use FamilyMedManager?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 65,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"FamilyMedManager offers both free and premium tiers. The free version includes core medication tracking features, while premium unlocks AI-powered recommendations and advanced analytics.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 66,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 64,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How does the AI search feature work?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 73,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Our AI feature analyzes your symptoms and available medication inventory to provide personalized recommendations and first aid guidance, powered by OpenAI's advanced language models.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 74,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 72,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I track medications for children and adults separately?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 81,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! FamilyMedManager allows you to create separate profiles for adults and children, with age-appropriate medication tracking and dosage recommendations for each family member.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 82,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 80,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How do medication reminders work?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"The app sends smart notifications based on your medication schedules. You can set custom reminder times, snooze options, and track when doses are taken to maintain accurate records.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"What happens if I lose my phone or device?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 97,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Since data is stored locally, we recommend regular backups. The app includes export functionality to save your medication data, and you can restore it on a new device.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I share medication information with my doctor?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Absolutely! FamilyMedManager allows you to export comprehensive medication reports that you can share with healthcare providers, including dosages, schedules, and adherence history.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How accurate is the inventory tracking?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 113,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"The inventory system tracks your medication usage based on scheduled doses and manual updates. It provides estimates for refill dates and low-stock alerts to help you stay prepared.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 114,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 112,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Does the app work without internet connection?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! FamilyMedManager is designed with offline-first architecture. All core features work without internet, though AI search requires connectivity to provide recommendations.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 122,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I set up medication schedules for complex regimens?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"The app supports flexible scheduling including multiple daily doses, weekly medications, as-needed prescriptions, and custom intervals to accommodate any medication regimen.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 130,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Is my medication data encrypted?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes, all medication data is encrypted both in storage and during any data transfers. We use industry-standard encryption to protect your sensitive health information.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 138,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 136,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How do I add a new family member?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: 'Simply navigate to the family management section and tap \"Add Family Member.\" You can create profiles for adults or children and assign medications to specific individuals.'\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 146,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I track over-the-counter medications and supplements?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! The app tracks all types of medications including prescriptions, over-the-counter drugs, vitamins, supplements, and herbal remedies with the same level of detail.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 154,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"What should I do in case of a medical emergency?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 161,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"FamilyMedManager provides quick access to your medication list for emergency responders, but always call emergency services first. The app includes emergency contact features for critical situations.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"How often should I update my medication information?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Update your information whenever there are changes to prescriptions, dosages, or schedules. Regular updates ensure accurate tracking and reliable AI recommendations.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 170,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Can I customize notification sounds and timing?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 177,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"Yes! You can personalize reminder notifications with custom sounds, vibration patterns, and timing preferences to fit your daily routine and ensure you never miss a dose.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"faq-item\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            children: \"Is there a limit to how many medications I can track?\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 29\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: \"No, there's no limit! You can track as many medications as needed for your entire family. The app is designed to handle complex medication regimens for households of any size.\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 29\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 47,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 17\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"support-hours\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Support Hours\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 196,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hours-info\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Email Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Monday - Friday, 9:00 AM - 6:00 PM EST\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Emergency Support:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" Available 24/7 for critical issues\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                            children: \"Response Time:\"\n                                        }, void 0, false, {\n                                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 28\n                                        }, this),\n                                        \" We typically respond within 24 hours during business days\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n                    lineNumber: 195,\n                    columnNumber: 17\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n            lineNumber: 7,\n            columnNumber: 13\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/contact.tsx\",\n        lineNumber: 6,\n        columnNumber: 9\n    }, this);\n}\n_c = Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./pages/contact.tsx\n"));

/***/ })

});