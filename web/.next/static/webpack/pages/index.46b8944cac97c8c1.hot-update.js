"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/index",{

/***/ "./pages/index.tsx":
/*!*************************!*\
  !*** ./pages/index.tsx ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Home; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"./node_modules/react/jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../components/Layout */ \"./components/Layout.tsx\");\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"hero\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"hero-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"hero-title\",\n                            children: \"FamilyMedManager \\uD83D\\uDC8A\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                            lineNumber: 9,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"hero-subtitle\",\n                            children: \"A comprehensive family medication management solution. Track medications, manage dosages, monitor inventory, and ensure your family's health needs are met efficiently.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                            lineNumber: 10,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hero-buttons\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"btn-primary\",\n                                    children: \"Get Started\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                    lineNumber: 15,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"btn-secondary\",\n                                    children: \"Learn More\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                    lineNumber: 16,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                            lineNumber: 14,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                    lineNumber: 8,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                lineNumber: 7,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"features-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"section-header\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                children: \"Comprehensive Family Health Management\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 24,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"section-subtitle\",\n                                children: \"Everything you need to keep your family's medications organized and accessible\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 25,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                        lineNumber: 23,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"features-grid\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"feature-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"feature-icon\",\n                                        children: \"\\uD83D\\uDC68‍\\uD83D\\uDC69‍\\uD83D\\uDC67‍\\uD83D\\uDC66\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Family Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Add and manage family members (adults and children) with personalized profiles and medication tracking.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 32,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 29,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"feature-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"feature-icon\",\n                                        children: \"\\uD83D\\uDC8A\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 36,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Medication Tracking\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Comprehensive medication database with detailed information, dosages, and administration schedules.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 38,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"feature-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"feature-icon\",\n                                        children: \"\\uD83D\\uDCC5\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 42,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Dosage Scheduling\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Set up and track medication schedules with smart reminders to never miss a dose.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 44,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 41,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"feature-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"feature-icon\",\n                                        children: \"\\uD83D\\uDCE6\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 48,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Inventory Management\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 49,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Monitor medication stock levels and expiration dates with automated refill reminders.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"feature-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"feature-icon\",\n                                        children: \"\\uD83D\\uDD0D\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"AI-Powered Search\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Get intelligent medication recommendations and first aid guidance based on symptoms and available inventory.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"feature-card\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"feature-icon\",\n                                        children: \"\\uD83D\\uDCF1\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        children: \"Cross-Platform\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 61,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Works seamlessly on iOS, Android, and Web with synchronized data across all devices.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                        lineNumber: 28,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                lineNumber: 22,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"benefits-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"section-header\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Why Choose FamilyMedManager?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"benefits-grid\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"benefit-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"\\uD83D\\uDD12 Secure & Private\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Your family's health data is stored securely with local SQLite database and offline functionality.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"benefit-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"\\uD83C\\uDFAF Smart Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 80,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"AI-powered search provides personalized medication recommendations and first aid guidance.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"benefit-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"⚡ Always Available\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 85,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Offline functionality ensures your medication information is always accessible when you need it.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 86,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 84,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"benefit-item\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        children: \"\\uD83D\\uDC68‍⚕️ Healthcare Ready\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 25\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        children: \"Organized medication records ready to share with healthcare providers and emergency responders.\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 25\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                lineNumber: 68,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"cta-section\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"cta-content\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            children: \"Ready to Take Control of Your Family's Health?\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"Join thousands of families who trust FamilyMedManager to keep their medication information organized and accessible.\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"cta-buttons\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"btn-primary large\",\n                                    children: \"Download Now\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    type: \"button\",\n                                    className: \"btn-outline large\",\n                                    children: \"View Demo\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                            lineNumber: 101,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n                lineNumber: 97,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/projects/FamilyMedManager/web/pages/index.tsx\",\n        lineNumber: 5,\n        columnNumber: 9\n    }, this);\n}\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9wYWdlcy9pbmRleC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBeUM7QUFFMUIsU0FBU0M7SUFDcEIscUJBQ0ksOERBQUNELDBEQUFNQTs7MEJBRUgsOERBQUNFO2dCQUFRQyxXQUFVOzBCQUNmLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBQ1gsOERBQUNFOzRCQUFHRixXQUFVO3NDQUFhOzs7Ozs7c0NBQzNCLDhEQUFDRzs0QkFBRUgsV0FBVTtzQ0FBZ0I7Ozs7OztzQ0FJN0IsOERBQUNDOzRCQUFJRCxXQUFVOzs4Q0FDWCw4REFBQ0k7b0NBQU9DLE1BQUs7b0NBQVNMLFdBQVU7OENBQWM7Ozs7Ozs4Q0FDOUMsOERBQUNJO29DQUFPQyxNQUFLO29DQUFTTCxXQUFVOzhDQUFnQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTTVELDhEQUFDRDtnQkFBUUMsV0FBVTs7a0NBQ2YsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDWCw4REFBQ007MENBQUc7Ozs7OzswQ0FDSiw4REFBQ0g7Z0NBQUVILFdBQVU7MENBQW1COzs7Ozs7Ozs7Ozs7a0NBR3BDLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ1gsOERBQUNDO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQWU7Ozs7OztrREFDOUIsOERBQUNPO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNKO2tEQUFFOzs7Ozs7Ozs7Ozs7MENBR1AsOERBQUNGO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQWU7Ozs7OztrREFDOUIsOERBQUNPO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNKO2tEQUFFOzs7Ozs7Ozs7Ozs7MENBR1AsOERBQUNGO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQWU7Ozs7OztrREFDOUIsOERBQUNPO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNKO2tEQUFFOzs7Ozs7Ozs7Ozs7MENBR1AsOERBQUNGO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQWU7Ozs7OztrREFDOUIsOERBQUNPO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNKO2tEQUFFOzs7Ozs7Ozs7Ozs7MENBR1AsOERBQUNGO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQWU7Ozs7OztrREFDOUIsOERBQUNPO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNKO2tEQUFFOzs7Ozs7Ozs7Ozs7MENBR1AsOERBQUNGO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ0M7d0NBQUlELFdBQVU7a0RBQWU7Ozs7OztrREFDOUIsOERBQUNPO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNKO2tEQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWYsOERBQUNKO2dCQUFRQyxXQUFVOztrQ0FDZiw4REFBQ0M7d0JBQUlELFdBQVU7a0NBQ1gsNEVBQUNNO3NDQUFHOzs7Ozs7Ozs7OztrQ0FHUiw4REFBQ0w7d0JBQUlELFdBQVU7OzBDQUNYLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ1gsOERBQUNRO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNMO2tEQUFFOzs7Ozs7Ozs7Ozs7MENBR1AsOERBQUNGO2dDQUFJRCxXQUFVOztrREFDWCw4REFBQ1E7a0RBQUc7Ozs7OztrREFDSiw4REFBQ0w7a0RBQUU7Ozs7Ozs7Ozs7OzswQ0FHUCw4REFBQ0Y7Z0NBQUlELFdBQVU7O2tEQUNYLDhEQUFDUTtrREFBRzs7Ozs7O2tEQUNKLDhEQUFDTDtrREFBRTs7Ozs7Ozs7Ozs7OzBDQUdQLDhEQUFDRjtnQ0FBSUQsV0FBVTs7a0RBQ1gsOERBQUNRO2tEQUFHOzs7Ozs7a0RBQ0osOERBQUNMO2tEQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBTWYsOERBQUNKO2dCQUFRQyxXQUFVOzBCQUNmLDRFQUFDQztvQkFBSUQsV0FBVTs7c0NBQ1gsOERBQUNNO3NDQUFHOzs7Ozs7c0NBQ0osOERBQUNIO3NDQUFFOzs7Ozs7c0NBQ0gsOERBQUNGOzRCQUFJRCxXQUFVOzs4Q0FDWCw4REFBQ0k7b0NBQU9DLE1BQUs7b0NBQVNMLFdBQVU7OENBQW9COzs7Ozs7OENBQ3BELDhEQUFDSTtvQ0FBT0MsTUFBSztvQ0FBU0wsV0FBVTs4Q0FBb0I7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBTTVFO0tBMUd3QkYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vcGFnZXMvaW5kZXgudHN4PzA3ZmYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IExheW91dCBmcm9tICcuLi9jb21wb25lbnRzL0xheW91dCdcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gSG9tZSgpIHtcbiAgICByZXR1cm4gKFxuICAgICAgICA8TGF5b3V0PlxuICAgICAgICAgICAgey8qIEhlcm8gU2VjdGlvbiAqL31cbiAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImhlcm9cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImhlcm8tY29udGVudFwiPlxuICAgICAgICAgICAgICAgICAgICA8aDEgY2xhc3NOYW1lPVwiaGVyby10aXRsZVwiPkZhbWlseU1lZE1hbmFnZXIg8J+SijwvaDE+XG4gICAgICAgICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cImhlcm8tc3VidGl0bGVcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIEEgY29tcHJlaGVuc2l2ZSBmYW1pbHkgbWVkaWNhdGlvbiBtYW5hZ2VtZW50IHNvbHV0aW9uLiBUcmFjayBtZWRpY2F0aW9ucywgbWFuYWdlIGRvc2FnZXMsXG4gICAgICAgICAgICAgICAgICAgICAgICBtb25pdG9yIGludmVudG9yeSwgYW5kIGVuc3VyZSB5b3VyIGZhbWlseSdzIGhlYWx0aCBuZWVkcyBhcmUgbWV0IGVmZmljaWVudGx5LlxuICAgICAgICAgICAgICAgICAgICA8L3A+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiaGVyby1idXR0b25zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIHR5cGU9XCJidXR0b25cIiBjbGFzc05hbWU9XCJidG4tcHJpbWFyeVwiPkdldCBTdGFydGVkPC9idXR0b24+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIHR5cGU9XCJidXR0b25cIiBjbGFzc05hbWU9XCJidG4tc2Vjb25kYXJ5XCI+TGVhcm4gTW9yZTwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvc2VjdGlvbj5cblxuICAgICAgICAgICAgey8qIEZlYXR1cmVzIFNlY3Rpb24gKi99XG4gICAgICAgICAgICA8c2VjdGlvbiBjbGFzc05hbWU9XCJmZWF0dXJlcy1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWN0aW9uLWhlYWRlclwiPlxuICAgICAgICAgICAgICAgICAgICA8aDI+Q29tcHJlaGVuc2l2ZSBGYW1pbHkgSGVhbHRoIE1hbmFnZW1lbnQ8L2gyPlxuICAgICAgICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJzZWN0aW9uLXN1YnRpdGxlXCI+RXZlcnl0aGluZyB5b3UgbmVlZCB0byBrZWVwIHlvdXIgZmFtaWx5J3MgbWVkaWNhdGlvbnMgb3JnYW5pemVkIGFuZCBhY2Nlc3NpYmxlPC9wPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmZWF0dXJlcy1ncmlkXCI+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmVhdHVyZS1jYXJkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZlYXR1cmUtaWNvblwiPvCfkajigI3wn5Gp4oCN8J+Rp+KAjfCfkaY8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMz5GYW1pbHkgTWFuYWdlbWVudDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5BZGQgYW5kIG1hbmFnZSBmYW1pbHkgbWVtYmVycyAoYWR1bHRzIGFuZCBjaGlsZHJlbikgd2l0aCBwZXJzb25hbGl6ZWQgcHJvZmlsZXMgYW5kIG1lZGljYXRpb24gdHJhY2tpbmcuPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZlYXR1cmUtY2FyZFwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmZWF0dXJlLWljb25cIj7wn5KKPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDM+TWVkaWNhdGlvbiBUcmFja2luZzwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5Db21wcmVoZW5zaXZlIG1lZGljYXRpb24gZGF0YWJhc2Ugd2l0aCBkZXRhaWxlZCBpbmZvcm1hdGlvbiwgZG9zYWdlcywgYW5kIGFkbWluaXN0cmF0aW9uIHNjaGVkdWxlcy48L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmVhdHVyZS1jYXJkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZlYXR1cmUtaWNvblwiPvCfk4U8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMz5Eb3NhZ2UgU2NoZWR1bGluZzwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5TZXQgdXAgYW5kIHRyYWNrIG1lZGljYXRpb24gc2NoZWR1bGVzIHdpdGggc21hcnQgcmVtaW5kZXJzIHRvIG5ldmVyIG1pc3MgYSBkb3NlLjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmZWF0dXJlLWNhcmRcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmVhdHVyZS1pY29uXCI+8J+TpjwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgICAgPGgzPkludmVudG9yeSBNYW5hZ2VtZW50PC9oMz5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxwPk1vbml0b3IgbWVkaWNhdGlvbiBzdG9jayBsZXZlbHMgYW5kIGV4cGlyYXRpb24gZGF0ZXMgd2l0aCBhdXRvbWF0ZWQgcmVmaWxsIHJlbWluZGVycy48L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmVhdHVyZS1jYXJkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZlYXR1cmUtaWNvblwiPvCflI08L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMz5BSS1Qb3dlcmVkIFNlYXJjaDwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5HZXQgaW50ZWxsaWdlbnQgbWVkaWNhdGlvbiByZWNvbW1lbmRhdGlvbnMgYW5kIGZpcnN0IGFpZCBndWlkYW5jZSBiYXNlZCBvbiBzeW1wdG9tcyBhbmQgYXZhaWxhYmxlIGludmVudG9yeS48L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmVhdHVyZS1jYXJkXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZlYXR1cmUtaWNvblwiPvCfk7E8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoMz5Dcm9zcy1QbGF0Zm9ybTwvaDM+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5Xb3JrcyBzZWFtbGVzc2x5IG9uIGlPUywgQW5kcm9pZCwgYW5kIFdlYiB3aXRoIHN5bmNocm9uaXplZCBkYXRhIGFjcm9zcyBhbGwgZGV2aWNlcy48L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9zZWN0aW9uPlxuXG4gICAgICAgICAgICB7LyogQmVuZWZpdHMgU2VjdGlvbiAqL31cbiAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImJlbmVmaXRzLXNlY3Rpb25cIj5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlY3Rpb24taGVhZGVyXCI+XG4gICAgICAgICAgICAgICAgICAgIDxoMj5XaHkgQ2hvb3NlIEZhbWlseU1lZE1hbmFnZXI/PC9oMj5cbiAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmVuZWZpdHMtZ3JpZFwiPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJlbmVmaXQtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0PvCflJIgU2VjdXJlICYgUHJpdmF0ZTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5Zb3VyIGZhbWlseSdzIGhlYWx0aCBkYXRhIGlzIHN0b3JlZCBzZWN1cmVseSB3aXRoIGxvY2FsIFNRTGl0ZSBkYXRhYmFzZSBhbmQgb2ZmbGluZSBmdW5jdGlvbmFsaXR5LjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiZW5lZml0LWl0ZW1cIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIDxoND7wn46vIFNtYXJ0IFJlY29tbWVuZGF0aW9uczwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5BSS1wb3dlcmVkIHNlYXJjaCBwcm92aWRlcyBwZXJzb25hbGl6ZWQgbWVkaWNhdGlvbiByZWNvbW1lbmRhdGlvbnMgYW5kIGZpcnN0IGFpZCBndWlkYW5jZS48L3A+XG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmVuZWZpdC1pdGVtXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8aDQ+4pqhIEFsd2F5cyBBdmFpbGFibGU8L2g0PlxuICAgICAgICAgICAgICAgICAgICAgICAgPHA+T2ZmbGluZSBmdW5jdGlvbmFsaXR5IGVuc3VyZXMgeW91ciBtZWRpY2F0aW9uIGluZm9ybWF0aW9uIGlzIGFsd2F5cyBhY2Nlc3NpYmxlIHdoZW4geW91IG5lZWQgaXQuPC9wPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJlbmVmaXQtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGg0PvCfkajigI3impXvuI8gSGVhbHRoY2FyZSBSZWFkeTwvaDQ+XG4gICAgICAgICAgICAgICAgICAgICAgICA8cD5Pcmdhbml6ZWQgbWVkaWNhdGlvbiByZWNvcmRzIHJlYWR5IHRvIHNoYXJlIHdpdGggaGVhbHRoY2FyZSBwcm92aWRlcnMgYW5kIGVtZXJnZW5jeSByZXNwb25kZXJzLjwvcD5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L3NlY3Rpb24+XG5cbiAgICAgICAgICAgIHsvKiBDVEEgU2VjdGlvbiAqL31cbiAgICAgICAgICAgIDxzZWN0aW9uIGNsYXNzTmFtZT1cImN0YS1zZWN0aW9uXCI+XG4gICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjdGEtY29udGVudFwiPlxuICAgICAgICAgICAgICAgICAgICA8aDI+UmVhZHkgdG8gVGFrZSBDb250cm9sIG9mIFlvdXIgRmFtaWx5J3MgSGVhbHRoPzwvaDI+XG4gICAgICAgICAgICAgICAgICAgIDxwPkpvaW4gdGhvdXNhbmRzIG9mIGZhbWlsaWVzIHdobyB0cnVzdCBGYW1pbHlNZWRNYW5hZ2VyIHRvIGtlZXAgdGhlaXIgbWVkaWNhdGlvbiBpbmZvcm1hdGlvbiBvcmdhbml6ZWQgYW5kIGFjY2Vzc2libGUuPC9wPlxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImN0YS1idXR0b25zXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICA8YnV0dG9uIHR5cGU9XCJidXR0b25cIiBjbGFzc05hbWU9XCJidG4tcHJpbWFyeSBsYXJnZVwiPkRvd25sb2FkIE5vdzwvYnV0dG9uPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgY2xhc3NOYW1lPVwiYnRuLW91dGxpbmUgbGFyZ2VcIj5WaWV3IERlbW88L2J1dHRvbj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L3NlY3Rpb24+XG4gICAgICAgIDwvTGF5b3V0PlxuICAgIClcbn1cbiJdLCJuYW1lcyI6WyJMYXlvdXQiLCJIb21lIiwic2VjdGlvbiIsImNsYXNzTmFtZSIsImRpdiIsImgxIiwicCIsImJ1dHRvbiIsInR5cGUiLCJoMiIsImgzIiwiaDQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///./pages/index.tsx\n"));

/***/ })

});