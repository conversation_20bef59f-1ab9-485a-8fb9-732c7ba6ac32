"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("pages/_app",{

/***/ "./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css":
/*!**********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css ***!
  \**********************************************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js */ \"./node_modules/next/dist/build/webpack/loaders/css-loader/src/runtime/api.js\");\n/* harmony import */ var _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0__);\n// Imports\n\nvar ___CSS_LOADER_EXPORT___ = _node_modules_next_dist_build_webpack_loaders_css_loader_src_runtime_api_js__WEBPACK_IMPORTED_MODULE_0___default()(true);\n// Module\n___CSS_LOADER_EXPORT___.push([module.id, \":root {\\n    --bg: #ffffff;\\n    --bg-secondary: #f8fafc;\\n    --bg-accent: #f0f9ff;\\n    --primary: #0ea5a4;\\n    --primary-dark: #0d9488;\\n    --primary-light: #5eead4;\\n    --secondary: #6366f1;\\n    --muted: #6b7280;\\n    --text-primary: #0f172a;\\n    --text-secondary: #475569;\\n    --text-muted: #94a3b8;\\n    --border: #e2e8f0;\\n    --border-light: #f1f5f9;\\n    --success: #10b981;\\n    --warning: #f59e0b;\\n    --error: #ef4444;\\n    --max-w: 1200px;\\n    --max-w-content: 900px;\\n    --border-radius: 12px;\\n    --border-radius-sm: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n}\\n\\n* {\\n    box-sizing: border-box;\\n}\\n\\nhtml,\\nbody,\\n#root {\\n    padding: 0;\\n    margin: 0;\\n    font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;\\n    background: var(--bg);\\n    color: var(--text-primary);\\n    line-height: 1.6;\\n}\\n\\n/* Layout */\\n.container {\\n    max-width: var(--max-w-content);\\n    margin: 0 auto;\\n    padding: 0 24px;\\n}\\n\\n.site-header {\\n    background: var(--bg);\\n    border-bottom: 1px solid var(--border);\\n    position: -webkit-sticky;\\n    position: sticky;\\n    top: 0;\\n    z-index: 100;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n}\\n\\n.site-header .container {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 20px 24px;\\n    max-width: var(--max-w);\\n}\\n\\n.nav {\\n    display: flex;\\n    gap: 32px;\\n}\\n\\n.nav a {\\n    color: var(--text-secondary);\\n    text-decoration: none;\\n    font-weight: 500;\\n    transition: color 0.2s ease;\\n    position: relative;\\n}\\n\\n.nav a:hover {\\n    color: var(--primary);\\n}\\n\\n.nav a::after {\\n    content: '';\\n    position: absolute;\\n    bottom: -4px;\\n    left: 0;\\n    width: 0;\\n    height: 2px;\\n    background: var(--primary);\\n    transition: width 0.2s ease;\\n}\\n\\n.nav a:hover::after {\\n    width: 100%;\\n}\\n\\n/* Typography */\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n    margin: 0 0 16px 0;\\n    font-weight: 700;\\n    line-height: 1.2;\\n    color: var(--text-primary);\\n}\\n\\nh1 {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n}\\n\\nh2 {\\n    font-size: 2.25rem;\\n    margin-bottom: 20px;\\n}\\n\\nh3 {\\n    font-size: 1.875rem;\\n    margin-bottom: 16px;\\n}\\n\\nh4 {\\n    font-size: 1.5rem;\\n    margin-bottom: 12px;\\n}\\n\\np {\\n    margin: 0 0 16px 0;\\n    color: var(--text-secondary);\\n}\\n\\nul,\\nol {\\n    margin: 0 0 16px 0;\\n    padding-left: 24px;\\n}\\n\\nli {\\n    margin-bottom: 8px;\\n    color: var(--text-secondary);\\n}\\n\\na {\\n    color: var(--primary);\\n    text-decoration: none;\\n    transition: color 0.2s ease;\\n}\\n\\na:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n/* Buttons */\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline {\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n    padding: 12px 24px;\\n    border-radius: var(--border-radius-sm);\\n    font-weight: 600;\\n    font-size: 16px;\\n    text-decoration: none;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n    gap: 8px;\\n}\\n\\n.btn-primary {\\n    background: var(--primary);\\n    color: white;\\n    box-shadow: var(--shadow);\\n}\\n\\n.btn-primary:hover {\\n    background: var(--primary-dark);\\n    transform: translateY(-1px);\\n    box-shadow: var(--shadow-lg);\\n    text-decoration: none;\\n    color: white;\\n}\\n\\n.btn-secondary {\\n    background: var(--bg-secondary);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border);\\n}\\n\\n.btn-secondary:hover {\\n    background: var(--border-light);\\n    transform: translateY(-1px);\\n    text-decoration: none;\\n    color: var(--text-primary);\\n}\\n\\n.btn-outline {\\n    background: transparent;\\n    color: var(--primary);\\n    border: 2px solid var(--primary);\\n}\\n\\n.btn-outline:hover {\\n    background: var(--primary);\\n    color: white;\\n    text-decoration: none;\\n}\\n\\n.btn-primary.large,\\n.btn-outline.large {\\n    padding: 16px 32px;\\n    font-size: 18px;\\n}\\n\\n/* Cards */\\n.card {\\n    background: var(--bg);\\n    border: 1px solid var(--border);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    box-shadow: var(--shadow);\\n    transition: all 0.2s ease;\\n}\\n\\n.card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n/* Hero Sections */\\n.hero {\\n    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg) 100%);\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.hero-content {\\n    max-width: 800px;\\n    margin: 0 auto;\\n}\\n\\n.hero-title {\\n    font-size: 3.5rem;\\n    margin-bottom: 24px;\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n}\\n\\n.hero-subtitle {\\n    font-size: 1.25rem;\\n    color: var(--text-secondary);\\n    margin-bottom: 40px;\\n    line-height: 1.6;\\n}\\n\\n.hero-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n.page-hero {\\n    background: var(--bg-secondary);\\n    padding: 60px 0;\\n    text-align: center;\\n    border-bottom: 1px solid var(--border);\\n}\\n\\n.last-updated {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n    margin-top: 16px;\\n}\\n\\n/* Sections */\\n.content-section {\\n    padding: 60px 0;\\n    border-bottom: 1px solid var(--border-light);\\n}\\n\\n.content-section:last-child {\\n    border-bottom: none;\\n}\\n\\n.section-header {\\n    text-align: center;\\n    margin-bottom: 60px;\\n}\\n\\n.section-subtitle {\\n    font-size: 1.125rem;\\n    color: var(--text-secondary);\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n/* Features Grid */\\n.features-section {\\n    padding: 80px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.features-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 60px;\\n}\\n\\n.feature-card {\\n    background: var(--bg);\\n    padding: 40px 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    box-shadow: var(--shadow);\\n    transition: all 0.3s ease;\\n    border: 1px solid var(--border);\\n}\\n\\n.feature-card:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-xl);\\n}\\n\\n.feature-icon {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n    display: block;\\n}\\n\\n.feature-card h3 {\\n    color: var(--text-primary);\\n    margin-bottom: 16px;\\n}\\n\\n.feature-card p {\\n    color: var(--text-secondary);\\n    line-height: 1.6;\\n}\\n\\n/* Benefits Grid */\\n.benefits-section {\\n    padding: 80px 0;\\n}\\n\\n.benefits-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.benefit-item {\\n    text-align: center;\\n    padding: 24px;\\n}\\n\\n.benefit-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n    font-size: 1.25rem;\\n}\\n\\n/* CTA Section */\\n.cta-section {\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\\n    color: white;\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.cta-content {\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n.cta-section h2 {\\n    color: white;\\n    margin-bottom: 24px;\\n}\\n\\n.cta-section p {\\n    color: rgba(255, 255, 255, 0.9);\\n    font-size: 1.125rem;\\n    margin-bottom: 40px;\\n}\\n\\n.cta-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n/* Values Grid */\\n.values-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.value-item {\\n    padding: 32px 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n}\\n\\n.value-item h3 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Tech List */\\n.tech-list {\\n    list-style: none;\\n    padding: 0;\\n    margin-top: 32px;\\n}\\n\\n.tech-list li {\\n    padding: 16px 0;\\n    border-bottom: 1px solid var(--border-light);\\n    display: flex;\\n    align-items: flex-start;\\n    gap: 12px;\\n}\\n\\n.tech-list li:last-child {\\n    border-bottom: none;\\n}\\n\\n.tech-list li::before {\\n    content: '✓';\\n    color: var(--success);\\n    font-weight: bold;\\n    flex-shrink: 0;\\n    margin-top: 2px;\\n}\\n\\n/* Contact Page Styles */\\n.contact-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-content {\\n    display: grid;\\n    grid-template-columns: 2fr 1fr;\\n    grid-gap: 60px;\\n    gap: 60px;\\n    margin: 60px 0;\\n}\\n\\n.contact-form-section h2 {\\n    margin-bottom: 16px;\\n}\\n\\n.contact-form {\\n    background: var(--bg-secondary);\\n    padding: 40px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.form-group {\\n    margin-bottom: 24px;\\n}\\n\\n.form-group label {\\n    display: block;\\n    margin-bottom: 8px;\\n    font-weight: 600;\\n    color: var(--text-primary);\\n}\\n\\n.form-input {\\n    width: 100%;\\n    padding: 12px 16px;\\n    border: 1px solid var(--border);\\n    border-radius: var(--border-radius-sm);\\n    font-size: 16px;\\n    transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n    background: var(--bg);\\n}\\n\\n.form-input:focus {\\n    outline: none;\\n    border-color: var(--primary);\\n    box-shadow: 0 0 0 3px rgba(14, 165, 164, 0.1);\\n}\\n\\n.form-input::placeholder {\\n    color: var(--text-muted);\\n}\\n\\ntextarea.form-input {\\n    resize: vertical;\\n    min-height: 120px;\\n}\\n\\n.contact-info-section {\\n    padding: 40px 0;\\n}\\n\\n.contact-methods {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 32px;\\n}\\n\\n.contact-method {\\n    padding: 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.contact-method h3 {\\n    margin-bottom: 12px;\\n    color: var(--primary);\\n}\\n\\n.contact-method p {\\n    margin-bottom: 8px;\\n}\\n\\n.contact-method small {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n}\\n\\n/* FAQ Styles */\\n.faq-section {\\n    padding: 60px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.faq-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    grid-gap: 32px;\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.faq-item {\\n    background: var(--bg);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.faq-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Support Hours */\\n.support-hours {\\n    padding: 40px 0;\\n    text-align: center;\\n}\\n\\n.hours-info {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    margin-top: 24px;\\n}\\n\\n.hours-info p {\\n    margin-bottom: 12px;\\n}\\n\\n/* Privacy Page Styles */\\n.privacy-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-info {\\n    background: var(--bg-secondary);\\n    padding: 24px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n    margin-top: 24px;\\n}\\n\\n.contact-info p {\\n    margin-bottom: 8px;\\n}\\n\\n.effective-date {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    margin-top: 60px;\\n    border: 1px solid var(--primary-light);\\n}\\n\\n/* About Page Styles */\\n.about-page {\\n    padding: 40px 0;\\n}\\n\\n/* Responsive Design */\\n@media (max-width: 768px) {\\n    .hero-title {\\n        font-size: 2.5rem;\\n    }\\n\\n    .hero-subtitle {\\n        font-size: 1.125rem;\\n    }\\n\\n    .hero-buttons {\\n        flex-direction: column;\\n        align-items: center;\\n    }\\n\\n    .features-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .benefits-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .values-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .contact-content {\\n        grid-template-columns: 1fr;\\n        gap: 40px;\\n    }\\n\\n    .faq-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .nav {\\n        gap: 16px;\\n    }\\n\\n    .container {\\n        padding: 0 16px;\\n    }\\n\\n    .site-header .container {\\n        padding: 16px;\\n    }\\n\\n    h1 {\\n        font-size: 2.25rem;\\n    }\\n\\n    h2 {\\n        font-size: 1.875rem;\\n    }\\n\\n    .content-section {\\n        padding: 40px 0;\\n    }\\n\\n    .features-section,\\n    .benefits-section,\\n    .cta-section {\\n        padding: 60px 0;\\n    }\\n}\\n\\n@media (max-width: 480px) {\\n    .hero-title {\\n        font-size: 2rem;\\n    }\\n\\n    .hero {\\n        padding: 60px 0;\\n    }\\n\\n    .feature-card,\\n    .contact-form {\\n        padding: 24px;\\n    }\\n\\n    .btn-primary.large,\\n    .btn-outline.large {\\n        padding: 14px 24px;\\n        font-size: 16px;\\n    }\\n}\", \"\",{\"version\":3,\"sources\":[\"webpack://styles/globals.css\"],\"names\":[],\"mappings\":\"AAAA;IACI,aAAa;IACb,uBAAuB;IACvB,oBAAoB;IACpB,kBAAkB;IAClB,uBAAuB;IACvB,wBAAwB;IACxB,oBAAoB;IACpB,gBAAgB;IAChB,uBAAuB;IACvB,yBAAyB;IACzB,qBAAqB;IACrB,iBAAiB;IACjB,uBAAuB;IACvB,kBAAkB;IAClB,kBAAkB;IAClB,gBAAgB;IAChB,eAAe;IACf,sBAAsB;IACtB,qBAAqB;IACrB,uBAAuB;IACvB,uEAAuE;IACvE,+EAA+E;IAC/E,gFAAgF;AACpF;;AAEA;IACI,sBAAsB;AAC1B;;AAEA;;;IAGI,UAAU;IACV,SAAS;IACT,yFAAyF;IACzF,qBAAqB;IACrB,0BAA0B;IAC1B,gBAAgB;AACpB;;AAEA,WAAW;AACX;IACI,+BAA+B;IAC/B,cAAc;IACd,eAAe;AACnB;;AAEA;IACI,qBAAqB;IACrB,sCAAsC;IACtC,wBAAgB;IAAhB,gBAAgB;IAChB,MAAM;IACN,YAAY;IACZ,mCAAmC;IACnC,2BAA2B;AAC/B;;AAEA;IACI,aAAa;IACb,mBAAmB;IACnB,8BAA8B;IAC9B,kBAAkB;IAClB,uBAAuB;AAC3B;;AAEA;IACI,aAAa;IACb,SAAS;AACb;;AAEA;IACI,4BAA4B;IAC5B,qBAAqB;IACrB,gBAAgB;IAChB,2BAA2B;IAC3B,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;AACzB;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,WAAW;IACX,0BAA0B;IAC1B,2BAA2B;AAC/B;;AAEA;IACI,WAAW;AACf;;AAEA,eAAe;AACf;;;;;;IAMI,kBAAkB;IAClB,gBAAgB;IAChB,gBAAgB;IAChB,0BAA0B;AAC9B;;AAEA;IACI,eAAe;IACf,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,iBAAiB;IACjB,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;AAChC;;AAEA;;IAEI,kBAAkB;IAClB,kBAAkB;AACtB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;AAChC;;AAEA;IACI,qBAAqB;IACrB,qBAAqB;IACrB,2BAA2B;AAC/B;;AAEA;IACI,0BAA0B;IAC1B,0BAA0B;AAC9B;;AAEA,YAAY;AACZ;;;IAGI,oBAAoB;IACpB,mBAAmB;IACnB,uBAAuB;IACvB,kBAAkB;IAClB,sCAAsC;IACtC,gBAAgB;IAChB,eAAe;IACf,qBAAqB;IACrB,YAAY;IACZ,eAAe;IACf,yBAAyB;IACzB,QAAQ;AACZ;;AAEA;IACI,0BAA0B;IAC1B,YAAY;IACZ,yBAAyB;AAC7B;;AAEA;IACI,+BAA+B;IAC/B,2BAA2B;IAC3B,4BAA4B;IAC5B,qBAAqB;IACrB,YAAY;AAChB;;AAEA;IACI,+BAA+B;IAC/B,0BAA0B;IAC1B,+BAA+B;AACnC;;AAEA;IACI,+BAA+B;IAC/B,2BAA2B;IAC3B,qBAAqB;IACrB,0BAA0B;AAC9B;;AAEA;IACI,uBAAuB;IACvB,qBAAqB;IACrB,gCAAgC;AACpC;;AAEA;IACI,0BAA0B;IAC1B,YAAY;IACZ,qBAAqB;AACzB;;AAEA;;IAEI,kBAAkB;IAClB,eAAe;AACnB;;AAEA,UAAU;AACV;IACI,qBAAqB;IACrB,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,yBAAyB;IACzB,yBAAyB;AAC7B;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA,kBAAkB;AAClB;IACI,wEAAwE;IACxE,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,cAAc;AAClB;;AAEA;IACI,iBAAiB;IACjB,mBAAmB;IACnB,6EAA6E;IAC7E,6BAA6B;IAC7B,oCAAoC;IACpC,qBAAqB;AACzB;;AAEA;IACI,kBAAkB;IAClB,4BAA4B;IAC5B,mBAAmB;IACnB,gBAAgB;AACpB;;AAEA;IACI,aAAa;IACb,SAAS;IACT,uBAAuB;IACvB,eAAe;AACnB;;AAEA;IACI,+BAA+B;IAC/B,eAAe;IACf,kBAAkB;IAClB,sCAAsC;AAC1C;;AAEA;IACI,wBAAwB;IACxB,eAAe;IACf,gBAAgB;AACpB;;AAEA,aAAa;AACb;IACI,eAAe;IACf,4CAA4C;AAChD;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,kBAAkB;IAClB,mBAAmB;AACvB;;AAEA;IACI,mBAAmB;IACnB,4BAA4B;IAC5B,gBAAgB;IAChB,cAAc;AAClB;;AAEA,kBAAkB;AAClB;IACI,eAAe;IACf,+BAA+B;AACnC;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,kBAAkB;IAClB,mCAAmC;IACnC,kBAAkB;IAClB,yBAAyB;IACzB,yBAAyB;IACzB,+BAA+B;AACnC;;AAEA;IACI,2BAA2B;IAC3B,4BAA4B;AAChC;;AAEA;IACI,eAAe;IACf,mBAAmB;IACnB,cAAc;AAClB;;AAEA;IACI,0BAA0B;IAC1B,mBAAmB;AACvB;;AAEA;IACI,4BAA4B;IAC5B,gBAAgB;AACpB;;AAEA,kBAAkB;AAClB;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,aAAa;AACjB;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;IACnB,kBAAkB;AACtB;;AAEA,gBAAgB;AAChB;IACI,gFAAgF;IAChF,YAAY;IACZ,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,gBAAgB;IAChB,cAAc;AAClB;;AAEA;IACI,YAAY;IACZ,mBAAmB;AACvB;;AAEA;IACI,+BAA+B;IAC/B,mBAAmB;IACnB,mBAAmB;AACvB;;AAEA;IACI,aAAa;IACb,SAAS;IACT,uBAAuB;IACvB,eAAe;AACnB;;AAEA,gBAAgB;AAChB;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;IAClB,+BAA+B;IAC/B,mCAAmC;IACnC,kBAAkB;AACtB;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;AACvB;;AAEA,cAAc;AACd;IACI,gBAAgB;IAChB,UAAU;IACV,gBAAgB;AACpB;;AAEA;IACI,eAAe;IACf,4CAA4C;IAC5C,aAAa;IACb,uBAAuB;IACvB,SAAS;AACb;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,YAAY;IACZ,qBAAqB;IACrB,iBAAiB;IACjB,cAAc;IACd,eAAe;AACnB;;AAEA,wBAAwB;AACxB;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,8BAA8B;IAC9B,cAAS;IAAT,SAAS;IACT,cAAc;AAClB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,+BAA+B;AACnC;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,cAAc;IACd,kBAAkB;IAClB,gBAAgB;IAChB,0BAA0B;AAC9B;;AAEA;IACI,WAAW;IACX,kBAAkB;IAClB,+BAA+B;IAC/B,sCAAsC;IACtC,eAAe;IACf,wDAAwD;IACxD,qBAAqB;AACzB;;AAEA;IACI,aAAa;IACb,4BAA4B;IAC5B,6CAA6C;AACjD;;AAEA;IACI,wBAAwB;AAC5B;;AAEA;IACI,gBAAgB;IAChB,iBAAiB;AACrB;;AAEA;IACI,eAAe;AACnB;;AAEA;IACI,aAAa;IACb,sBAAsB;IACtB,SAAS;AACb;;AAEA;IACI,aAAa;IACb,+BAA+B;IAC/B,mCAAmC;IACnC,+BAA+B;AACnC;;AAEA;IACI,mBAAmB;IACnB,qBAAqB;AACzB;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,wBAAwB;IACxB,eAAe;AACnB;;AAEA,eAAe;AACf;IACI,eAAe;IACf,+BAA+B;AACnC;;AAEA;IACI,aAAa;IACb,2DAA2D;IAC3D,cAAS;IAAT,SAAS;IACT,gBAAgB;AACpB;;AAEA;IACI,qBAAqB;IACrB,aAAa;IACb,mCAAmC;IACnC,+BAA+B;AACnC;;AAEA;IACI,qBAAqB;IACrB,mBAAmB;AACvB;;AAEA,kBAAkB;AAClB;IACI,eAAe;IACf,kBAAkB;AACtB;;AAEA;IACI,4BAA4B;IAC5B,aAAa;IACb,mCAAmC;IACnC,gBAAgB;AACpB;;AAEA;IACI,mBAAmB;AACvB;;AAEA,wBAAwB;AACxB;IACI,eAAe;AACnB;;AAEA;IACI,+BAA+B;IAC/B,aAAa;IACb,mCAAmC;IACnC,+BAA+B;IAC/B,gBAAgB;AACpB;;AAEA;IACI,kBAAkB;AACtB;;AAEA;IACI,4BAA4B;IAC5B,aAAa;IACb,mCAAmC;IACnC,kBAAkB;IAClB,gBAAgB;IAChB,sCAAsC;AAC1C;;AAEA,sBAAsB;AACtB;IACI,eAAe;AACnB;;AAEA,sBAAsB;AACtB;IACI;QACI,iBAAiB;IACrB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,sBAAsB;QACtB,mBAAmB;IACvB;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,0BAA0B;QAC1B,SAAS;IACb;;IAEA;QACI,SAAS;IACb;;IAEA;QACI,eAAe;IACnB;;IAEA;QACI,aAAa;IACjB;;IAEA;QACI,kBAAkB;IACtB;;IAEA;QACI,mBAAmB;IACvB;;IAEA;QACI,eAAe;IACnB;;IAEA;;;QAGI,eAAe;IACnB;AACJ;;AAEA;IACI;QACI,eAAe;IACnB;;IAEA;QACI,eAAe;IACnB;;IAEA;;QAEI,aAAa;IACjB;;IAEA;;QAEI,kBAAkB;QAClB,eAAe;IACnB;AACJ\",\"sourcesContent\":[\":root {\\n    --bg: #ffffff;\\n    --bg-secondary: #f8fafc;\\n    --bg-accent: #f0f9ff;\\n    --primary: #0ea5a4;\\n    --primary-dark: #0d9488;\\n    --primary-light: #5eead4;\\n    --secondary: #6366f1;\\n    --muted: #6b7280;\\n    --text-primary: #0f172a;\\n    --text-secondary: #475569;\\n    --text-muted: #94a3b8;\\n    --border: #e2e8f0;\\n    --border-light: #f1f5f9;\\n    --success: #10b981;\\n    --warning: #f59e0b;\\n    --error: #ef4444;\\n    --max-w: 1200px;\\n    --max-w-content: 900px;\\n    --border-radius: 12px;\\n    --border-radius-sm: 8px;\\n    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\\n    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\\n    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\\n}\\n\\n* {\\n    box-sizing: border-box;\\n}\\n\\nhtml,\\nbody,\\n#root {\\n    padding: 0;\\n    margin: 0;\\n    font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;\\n    background: var(--bg);\\n    color: var(--text-primary);\\n    line-height: 1.6;\\n}\\n\\n/* Layout */\\n.container {\\n    max-width: var(--max-w-content);\\n    margin: 0 auto;\\n    padding: 0 24px;\\n}\\n\\n.site-header {\\n    background: var(--bg);\\n    border-bottom: 1px solid var(--border);\\n    position: sticky;\\n    top: 0;\\n    z-index: 100;\\n    -webkit-backdrop-filter: blur(10px);\\n    backdrop-filter: blur(10px);\\n}\\n\\n.site-header .container {\\n    display: flex;\\n    align-items: center;\\n    justify-content: space-between;\\n    padding: 20px 24px;\\n    max-width: var(--max-w);\\n}\\n\\n.nav {\\n    display: flex;\\n    gap: 32px;\\n}\\n\\n.nav a {\\n    color: var(--text-secondary);\\n    text-decoration: none;\\n    font-weight: 500;\\n    transition: color 0.2s ease;\\n    position: relative;\\n}\\n\\n.nav a:hover {\\n    color: var(--primary);\\n}\\n\\n.nav a::after {\\n    content: '';\\n    position: absolute;\\n    bottom: -4px;\\n    left: 0;\\n    width: 0;\\n    height: 2px;\\n    background: var(--primary);\\n    transition: width 0.2s ease;\\n}\\n\\n.nav a:hover::after {\\n    width: 100%;\\n}\\n\\n/* Typography */\\nh1,\\nh2,\\nh3,\\nh4,\\nh5,\\nh6 {\\n    margin: 0 0 16px 0;\\n    font-weight: 700;\\n    line-height: 1.2;\\n    color: var(--text-primary);\\n}\\n\\nh1 {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n}\\n\\nh2 {\\n    font-size: 2.25rem;\\n    margin-bottom: 20px;\\n}\\n\\nh3 {\\n    font-size: 1.875rem;\\n    margin-bottom: 16px;\\n}\\n\\nh4 {\\n    font-size: 1.5rem;\\n    margin-bottom: 12px;\\n}\\n\\np {\\n    margin: 0 0 16px 0;\\n    color: var(--text-secondary);\\n}\\n\\nul,\\nol {\\n    margin: 0 0 16px 0;\\n    padding-left: 24px;\\n}\\n\\nli {\\n    margin-bottom: 8px;\\n    color: var(--text-secondary);\\n}\\n\\na {\\n    color: var(--primary);\\n    text-decoration: none;\\n    transition: color 0.2s ease;\\n}\\n\\na:hover {\\n    color: var(--primary-dark);\\n    text-decoration: underline;\\n}\\n\\n/* Buttons */\\n.btn-primary,\\n.btn-secondary,\\n.btn-outline {\\n    display: inline-flex;\\n    align-items: center;\\n    justify-content: center;\\n    padding: 12px 24px;\\n    border-radius: var(--border-radius-sm);\\n    font-weight: 600;\\n    font-size: 16px;\\n    text-decoration: none;\\n    border: none;\\n    cursor: pointer;\\n    transition: all 0.2s ease;\\n    gap: 8px;\\n}\\n\\n.btn-primary {\\n    background: var(--primary);\\n    color: white;\\n    box-shadow: var(--shadow);\\n}\\n\\n.btn-primary:hover {\\n    background: var(--primary-dark);\\n    transform: translateY(-1px);\\n    box-shadow: var(--shadow-lg);\\n    text-decoration: none;\\n    color: white;\\n}\\n\\n.btn-secondary {\\n    background: var(--bg-secondary);\\n    color: var(--text-primary);\\n    border: 1px solid var(--border);\\n}\\n\\n.btn-secondary:hover {\\n    background: var(--border-light);\\n    transform: translateY(-1px);\\n    text-decoration: none;\\n    color: var(--text-primary);\\n}\\n\\n.btn-outline {\\n    background: transparent;\\n    color: var(--primary);\\n    border: 2px solid var(--primary);\\n}\\n\\n.btn-outline:hover {\\n    background: var(--primary);\\n    color: white;\\n    text-decoration: none;\\n}\\n\\n.btn-primary.large,\\n.btn-outline.large {\\n    padding: 16px 32px;\\n    font-size: 18px;\\n}\\n\\n/* Cards */\\n.card {\\n    background: var(--bg);\\n    border: 1px solid var(--border);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    box-shadow: var(--shadow);\\n    transition: all 0.2s ease;\\n}\\n\\n.card:hover {\\n    transform: translateY(-2px);\\n    box-shadow: var(--shadow-lg);\\n}\\n\\n/* Hero Sections */\\n.hero {\\n    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg) 100%);\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.hero-content {\\n    max-width: 800px;\\n    margin: 0 auto;\\n}\\n\\n.hero-title {\\n    font-size: 3.5rem;\\n    margin-bottom: 24px;\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);\\n    -webkit-background-clip: text;\\n    -webkit-text-fill-color: transparent;\\n    background-clip: text;\\n}\\n\\n.hero-subtitle {\\n    font-size: 1.25rem;\\n    color: var(--text-secondary);\\n    margin-bottom: 40px;\\n    line-height: 1.6;\\n}\\n\\n.hero-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n.page-hero {\\n    background: var(--bg-secondary);\\n    padding: 60px 0;\\n    text-align: center;\\n    border-bottom: 1px solid var(--border);\\n}\\n\\n.last-updated {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n    margin-top: 16px;\\n}\\n\\n/* Sections */\\n.content-section {\\n    padding: 60px 0;\\n    border-bottom: 1px solid var(--border-light);\\n}\\n\\n.content-section:last-child {\\n    border-bottom: none;\\n}\\n\\n.section-header {\\n    text-align: center;\\n    margin-bottom: 60px;\\n}\\n\\n.section-subtitle {\\n    font-size: 1.125rem;\\n    color: var(--text-secondary);\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n/* Features Grid */\\n.features-section {\\n    padding: 80px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.features-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n    gap: 32px;\\n    margin-top: 60px;\\n}\\n\\n.feature-card {\\n    background: var(--bg);\\n    padding: 40px 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    box-shadow: var(--shadow);\\n    transition: all 0.3s ease;\\n    border: 1px solid var(--border);\\n}\\n\\n.feature-card:hover {\\n    transform: translateY(-4px);\\n    box-shadow: var(--shadow-xl);\\n}\\n\\n.feature-icon {\\n    font-size: 3rem;\\n    margin-bottom: 24px;\\n    display: block;\\n}\\n\\n.feature-card h3 {\\n    color: var(--text-primary);\\n    margin-bottom: 16px;\\n}\\n\\n.feature-card p {\\n    color: var(--text-secondary);\\n    line-height: 1.6;\\n}\\n\\n/* Benefits Grid */\\n.benefits-section {\\n    padding: 80px 0;\\n}\\n\\n.benefits-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.benefit-item {\\n    text-align: center;\\n    padding: 24px;\\n}\\n\\n.benefit-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n    font-size: 1.25rem;\\n}\\n\\n/* CTA Section */\\n.cta-section {\\n    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);\\n    color: white;\\n    padding: 80px 0;\\n    text-align: center;\\n}\\n\\n.cta-content {\\n    max-width: 600px;\\n    margin: 0 auto;\\n}\\n\\n.cta-section h2 {\\n    color: white;\\n    margin-bottom: 24px;\\n}\\n\\n.cta-section p {\\n    color: rgba(255, 255, 255, 0.9);\\n    font-size: 1.125rem;\\n    margin-bottom: 40px;\\n}\\n\\n.cta-buttons {\\n    display: flex;\\n    gap: 16px;\\n    justify-content: center;\\n    flex-wrap: wrap;\\n}\\n\\n/* Values Grid */\\n.values-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.value-item {\\n    padding: 32px 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n}\\n\\n.value-item h3 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Tech List */\\n.tech-list {\\n    list-style: none;\\n    padding: 0;\\n    margin-top: 32px;\\n}\\n\\n.tech-list li {\\n    padding: 16px 0;\\n    border-bottom: 1px solid var(--border-light);\\n    display: flex;\\n    align-items: flex-start;\\n    gap: 12px;\\n}\\n\\n.tech-list li:last-child {\\n    border-bottom: none;\\n}\\n\\n.tech-list li::before {\\n    content: '✓';\\n    color: var(--success);\\n    font-weight: bold;\\n    flex-shrink: 0;\\n    margin-top: 2px;\\n}\\n\\n/* Contact Page Styles */\\n.contact-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-content {\\n    display: grid;\\n    grid-template-columns: 2fr 1fr;\\n    gap: 60px;\\n    margin: 60px 0;\\n}\\n\\n.contact-form-section h2 {\\n    margin-bottom: 16px;\\n}\\n\\n.contact-form {\\n    background: var(--bg-secondary);\\n    padding: 40px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.form-group {\\n    margin-bottom: 24px;\\n}\\n\\n.form-group label {\\n    display: block;\\n    margin-bottom: 8px;\\n    font-weight: 600;\\n    color: var(--text-primary);\\n}\\n\\n.form-input {\\n    width: 100%;\\n    padding: 12px 16px;\\n    border: 1px solid var(--border);\\n    border-radius: var(--border-radius-sm);\\n    font-size: 16px;\\n    transition: border-color 0.2s ease, box-shadow 0.2s ease;\\n    background: var(--bg);\\n}\\n\\n.form-input:focus {\\n    outline: none;\\n    border-color: var(--primary);\\n    box-shadow: 0 0 0 3px rgba(14, 165, 164, 0.1);\\n}\\n\\n.form-input::placeholder {\\n    color: var(--text-muted);\\n}\\n\\ntextarea.form-input {\\n    resize: vertical;\\n    min-height: 120px;\\n}\\n\\n.contact-info-section {\\n    padding: 40px 0;\\n}\\n\\n.contact-methods {\\n    display: flex;\\n    flex-direction: column;\\n    gap: 32px;\\n}\\n\\n.contact-method {\\n    padding: 24px;\\n    background: var(--bg-secondary);\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.contact-method h3 {\\n    margin-bottom: 12px;\\n    color: var(--primary);\\n}\\n\\n.contact-method p {\\n    margin-bottom: 8px;\\n}\\n\\n.contact-method small {\\n    color: var(--text-muted);\\n    font-size: 14px;\\n}\\n\\n/* FAQ Styles */\\n.faq-section {\\n    padding: 60px 0;\\n    background: var(--bg-secondary);\\n}\\n\\n.faq-grid {\\n    display: grid;\\n    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n    gap: 32px;\\n    margin-top: 40px;\\n}\\n\\n.faq-item {\\n    background: var(--bg);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n}\\n\\n.faq-item h4 {\\n    color: var(--primary);\\n    margin-bottom: 16px;\\n}\\n\\n/* Support Hours */\\n.support-hours {\\n    padding: 40px 0;\\n    text-align: center;\\n}\\n\\n.hours-info {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    margin-top: 24px;\\n}\\n\\n.hours-info p {\\n    margin-bottom: 12px;\\n}\\n\\n/* Privacy Page Styles */\\n.privacy-page {\\n    padding: 40px 0;\\n}\\n\\n.contact-info {\\n    background: var(--bg-secondary);\\n    padding: 24px;\\n    border-radius: var(--border-radius);\\n    border: 1px solid var(--border);\\n    margin-top: 24px;\\n}\\n\\n.contact-info p {\\n    margin-bottom: 8px;\\n}\\n\\n.effective-date {\\n    background: var(--bg-accent);\\n    padding: 32px;\\n    border-radius: var(--border-radius);\\n    text-align: center;\\n    margin-top: 60px;\\n    border: 1px solid var(--primary-light);\\n}\\n\\n/* About Page Styles */\\n.about-page {\\n    padding: 40px 0;\\n}\\n\\n/* Responsive Design */\\n@media (max-width: 768px) {\\n    .hero-title {\\n        font-size: 2.5rem;\\n    }\\n\\n    .hero-subtitle {\\n        font-size: 1.125rem;\\n    }\\n\\n    .hero-buttons {\\n        flex-direction: column;\\n        align-items: center;\\n    }\\n\\n    .features-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .benefits-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .values-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .contact-content {\\n        grid-template-columns: 1fr;\\n        gap: 40px;\\n    }\\n\\n    .faq-grid {\\n        grid-template-columns: 1fr;\\n        gap: 24px;\\n    }\\n\\n    .nav {\\n        gap: 16px;\\n    }\\n\\n    .container {\\n        padding: 0 16px;\\n    }\\n\\n    .site-header .container {\\n        padding: 16px;\\n    }\\n\\n    h1 {\\n        font-size: 2.25rem;\\n    }\\n\\n    h2 {\\n        font-size: 1.875rem;\\n    }\\n\\n    .content-section {\\n        padding: 40px 0;\\n    }\\n\\n    .features-section,\\n    .benefits-section,\\n    .cta-section {\\n        padding: 60px 0;\\n    }\\n}\\n\\n@media (max-width: 480px) {\\n    .hero-title {\\n        font-size: 2rem;\\n    }\\n\\n    .hero {\\n        padding: 60px 0;\\n    }\\n\\n    .feature-card,\\n    .contact-form {\\n        padding: 24px;\\n    }\\n\\n    .btn-primary.large,\\n    .btn-outline.large {\\n        padding: 14px 24px;\\n        font-size: 16px;\\n    }\\n}\"],\"sourceRoot\":\"\"}]);\n// Exports\n/* harmony default export */ __webpack_exports__[\"default\"] = (___CSS_LOADER_EXPORT___);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[1]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[7].oneOf[14].use[2]!./styles/globals.css\n"));

/***/ })

});