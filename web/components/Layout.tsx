import Link from 'next/link'
import React from 'react'

export const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    return (
        <div>
            <header className="site-header">
                <div className="container">
                    <div className="site-logo">
                        <strong>
                            FamilyMedManager 💊
                        </strong>
                    </div>
                    <nav className="nav">
                        <Link href="/">Home</Link>
                        <Link href="/about">About</Link>
                        <Link href="/contact">Contact</Link>
                        <Link href="/privacy">Privacy</Link>
                    </nav>
                </div>
            </header>
            <main>
                <div className="container">
                    {children}
                </div>
            </main>
        </div>
    )
}

export default Layout
