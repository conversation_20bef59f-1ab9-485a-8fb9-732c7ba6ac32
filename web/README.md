# FamilyMedManager Website

A comprehensive static website for FamilyMedManager - a family medication management application. Built with Next.js and designed for optimal performance and user experience.

## Features

- **Landing Page**: Comprehensive feature showcase with hero section, benefits, and call-to-action
- **About Us**: Detailed company information, mission, values, and technology stack
- **Contact**: Direct email contact with extensive FAQ section (15+ questions)
- **Privacy Policy**: Comprehensive privacy policy covering data collection and user rights
- **Responsive Design**: Mobile-first approach with cross-browser compatibility
- **SEO Optimized**: Proper meta tags, structured data, and semantic HTML

## Getting Started

### Prerequisites
- Node.js 16.x or later
- npm or yarn

### Installation

1. Install dependencies:
```bash
npm install
```

2. Run the development server:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) to view the website

### Build for Production

```bash
npm run build
npm start
```

## Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set the root directory to `web`
3. Vercel will automatically detect Next.js and deploy

### Manual Deployment

1. Build the project: `npm run build`
2. Deploy the `.next` folder and `public` directory to your hosting provider

## Project Structure

```
web/
├── components/          # React components
│   └── Layout.tsx      # Main layout component
├── pages/              # Next.js pages
│   ├── _app.tsx       # App configuration
│   ├── index.tsx      # Landing page
│   ├── about.tsx      # About Us page
│   ├── contact.tsx    # Contact page
│   └── privacy.tsx    # Privacy Policy page
├── public/             # Static assets
│   ├── favicon.svg    # Site favicon
│   ├── manifest.webmanifest # PWA manifest
│   └── robots.txt     # SEO robots file
├── styles/             # CSS styles
│   └── globals.css    # Global styles
└── vercel.json        # Vercel configuration
```

## Contact Information

All contact forms and inquiries are directed to: <EMAIL>

## Technologies Used

- **Framework**: Next.js 14
- **Language**: TypeScript
- **Styling**: CSS Custom Properties (CSS Variables)
- **Deployment**: Vercel
- **Icons**: Unicode Emojis for cross-platform compatibility

## License

This project is part of the FamilyMedManager application suite.

