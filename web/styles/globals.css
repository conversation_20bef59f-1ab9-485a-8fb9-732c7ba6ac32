:root {
    --bg: #ffffff;
    --muted: #6b7280;
    --accent: #0ea5a4;
    --max-w: 900px;
}

html,
body,
#root {
    padding: 0;
    margin: 0;
    font-family: Inter, system-ui, -apple-system, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>;
    background: var(--bg);
    color: #0f172a;
}

.container {
    max-width: var(--max-w);
    margin: 48px auto;
    padding: 0 16px;
}

.site-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 16px;
    border-bottom: 1px solid #eef2f7;
}

.nav a {
    margin-left: 12px;
    color: var(--muted);
    text-decoration: none
}

.hero {
    padding: 48px 0
}

.card {
    background: #fff;
    border: 1px solid #eef2f7;
    padding: 24px;
    border-radius: 8px
}