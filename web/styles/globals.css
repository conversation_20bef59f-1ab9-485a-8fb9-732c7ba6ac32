:root {
    --bg: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-accent: #f0f9ff;
    --primary: #0ea5a4;
    --primary-dark: #0d9488;
    --primary-light: #5eead4;
    --secondary: #6366f1;
    --muted: #6b7280;
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #94a3b8;
    --border: #e2e8f0;
    --border-light: #f1f5f9;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --max-w: 1200px;
    --max-w-content: 900px;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

* {
    box-sizing: border-box;
}

html,
body,
#root {
    padding: 0;
    margin: 0;
    font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial;
    background: var(--bg);
    color: var(--text-primary);
    line-height: 1.6;
}

/* Layout */
.container {
    max-width: var(--max-w-content);
    margin: 0 auto;
    padding: 0 24px;
}

.site-header {
    background: var(--bg);
    border-bottom: 1px solid var(--border);
    position: sticky;
    top: 0;
    z-index: 100;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.site-header .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 24px;
    max-width: var(--max-w);
}

.site-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.site-logo strong {
    font-size: 1.25rem;
    color: var(--primary);
}

.nav {
    display: flex;
    gap: 32px;
}

.nav a {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
    position: relative;
}

.nav a:hover {
    color: var(--primary);
}

.nav a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary);
    transition: width 0.2s ease;
}

.nav a:hover::after {
    width: 100%;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0 0 16px 0;
    font-weight: 700;
    line-height: 1.2;
    color: var(--text-primary);
}

h1 {
    font-size: 3rem;
    margin-bottom: 24px;
}

h2 {
    font-size: 2.25rem;
    margin-bottom: 20px;
}

h3 {
    font-size: 1.875rem;
    margin-bottom: 16px;
}

h4 {
    font-size: 1.5rem;
    margin-bottom: 12px;
}

p {
    margin: 0 0 16px 0;
    color: var(--text-secondary);
}

ul,
ol {
    margin: 0 0 16px 0;
    padding-left: 24px;
}

li {
    margin-bottom: 8px;
    color: var(--text-secondary);
}

a {
    color: var(--primary);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

/* Buttons */
.btn-primary,
.btn-secondary,
.btn-outline {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 16px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 8px;
}

.btn-primary {
    background: var(--primary);
    color: white;
    box-shadow: var(--shadow);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
    text-decoration: none;
    color: white;
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background: var(--border-light);
    transform: translateY(-1px);
    text-decoration: none;
    color: var(--text-primary);
}

.btn-outline {
    background: transparent;
    color: var(--primary);
    border: 2px solid var(--primary);
}

.btn-outline:hover {
    background: var(--primary);
    color: white;
    text-decoration: none;
}

.btn-primary.large,
.btn-outline.large {
    padding: 16px 32px;
    font-size: 18px;
}

/* Cards */
.card {
    background: var(--bg);
    border: 1px solid var(--border);
    padding: 32px;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow);
    transition: all 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Hero Sections */
.hero {
    background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg) 100%);
    padding: 80px 0;
    text-align: center;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
}

.hero-title {
    font-size: 3.5rem;
    margin-bottom: 24px;
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.6;
}

.hero-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

.page-hero {
    background: var(--bg-secondary);
    padding: 60px 0;
    text-align: center;
    border-bottom: 1px solid var(--border);
}

.last-updated {
    color: var(--text-muted);
    font-size: 14px;
    margin-top: 16px;
}

/* Sections */
.content-section {
    padding: 60px 0;
    border-bottom: 1px solid var(--border-light);
}

.content-section:last-child {
    border-bottom: none;
}

.section-header {
    text-align: center;
    margin-bottom: 60px;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* Features Grid */
.features-section {
    padding: 80px 0;
    background: var(--bg-secondary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 32px;
    margin-top: 60px;
}

.feature-card {
    background: var(--bg);
    padding: 40px 32px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    border: 1px solid var(--border);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 24px;
    display: block;
}

.feature-card h3 {
    color: var(--text-primary);
    margin-bottom: 16px;
}

.feature-card p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Benefits Grid */
.benefits-section {
    padding: 80px 0;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 32px;
    margin-top: 40px;
}

.benefit-item {
    text-align: center;
    padding: 24px;
}

.benefit-item h4 {
    color: var(--primary);
    margin-bottom: 16px;
    font-size: 1.25rem;
}

/* CTA Section */
.cta-section {
    background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 80px 0;
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-section h2 {
    color: white;
    margin-bottom: 24px;
}

.cta-section p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.125rem;
    margin-bottom: 40px;
}

.cta-buttons {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
}

/* Values Grid */
.values-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 32px;
    margin-top: 40px;
}

.value-item {
    padding: 32px 24px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    text-align: center;
}

.value-item h3 {
    color: var(--primary);
    margin-bottom: 16px;
}

/* Tech List */
.tech-list {
    list-style: none;
    padding: 0;
    margin-top: 32px;
}

.tech-list li {
    padding: 16px 0;
    border-bottom: 1px solid var(--border-light);
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.tech-list li:last-child {
    border-bottom: none;
}

.tech-list li::before {
    content: '✓';
    color: var(--success);
    font-weight: bold;
    flex-shrink: 0;
    margin-top: 2px;
}

/* Contact Page Styles */
.contact-page {
    padding: 40px 0;
}

.contact-content {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 60px;
    margin: 60px 0;
}

.contact-form-section h2 {
    margin-bottom: 16px;
}

.contact-form {
    background: var(--bg-secondary);
    padding: 40px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
}

.form-group {
    margin-bottom: 24px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-primary);
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid var(--border);
    border-radius: var(--border-radius-sm);
    font-size: 16px;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
    background: var(--bg);
}

.form-input:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(14, 165, 164, 0.1);
}

.form-input::placeholder {
    color: var(--text-muted);
}

textarea.form-input {
    resize: vertical;
    min-height: 120px;
}

.contact-info-section {
    padding: 40px 0;
}

.contact-methods {
    display: flex;
    flex-direction: column;
    gap: 32px;
}

.contact-method {
    padding: 24px;
    background: var(--bg-secondary);
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
}

.contact-method h3 {
    margin-bottom: 12px;
    color: var(--primary);
}

.contact-method p {
    margin-bottom: 8px;
}

.contact-method small {
    color: var(--text-muted);
    font-size: 14px;
}

/* FAQ Styles */
.faq-section {
    padding: 60px 0;
    background: var(--bg-secondary);
}

.faq-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 32px;
    margin-top: 40px;
}

.faq-item {
    background: var(--bg);
    padding: 32px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
}

.faq-item h4 {
    color: var(--primary);
    margin-bottom: 16px;
}

/* Support Hours */
.support-hours {
    padding: 40px 0;
    text-align: center;
}

.hours-info {
    background: var(--bg-accent);
    padding: 32px;
    border-radius: var(--border-radius);
    margin-top: 24px;
}

.hours-info p {
    margin-bottom: 12px;
}

/* Privacy Page Styles */
.privacy-page {
    padding: 40px 0;
}

.contact-info {
    background: var(--bg-secondary);
    padding: 24px;
    border-radius: var(--border-radius);
    border: 1px solid var(--border);
    margin-top: 24px;
}

.contact-info p {
    margin-bottom: 8px;
}

.effective-date {
    background: var(--bg-accent);
    padding: 32px;
    border-radius: var(--border-radius);
    text-align: center;
    margin-top: 60px;
    border: 1px solid var(--primary-light);
}

/* About Page Styles */
.about-page {
    padding: 40px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.125rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .values-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .faq-grid {
        grid-template-columns: 1fr;
        gap: 24px;
    }

    .nav {
        gap: 16px;
    }

    .container {
        padding: 0 16px;
    }

    .site-header .container {
        padding: 16px;
    }

    h1 {
        font-size: 2.25rem;
    }

    h2 {
        font-size: 1.875rem;
    }

    .content-section {
        padding: 40px 0;
    }

    .features-section,
    .benefits-section,
    .cta-section {
        padding: 60px 0;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }

    .hero {
        padding: 60px 0;
    }

    .feature-card,
    .contact-form {
        padding: 24px;
    }

    .btn-primary.large,
    .btn-outline.large {
        padding: 14px 24px;
        font-size: 16px;
    }
}